---
output:
  html_document: default
  pdf_document: default
---

# Metadatos ICFES
icfes:
  competencia:
    - interpretacion_representacion
    - formulacion_ejecucion
  nivel_dificultad: 3
  contenido:
    categoria: estadistica
    tipo: generico
  contexto: comunitario
  eje_axial: eje4
  componente: aleatorio

```{r GeneracionDeDatos, echo = FALSE, results = "hide"}

# Configuración inicial para no mostrar código, advertencias ni mensajes en el documento final
knitr::opts_chunk$set(echo = FALSE, warning = FALSE, message = FALSE)

# Generación de Datos
# Cargar las librerías necesarias
library(exams)
library(knitr)

# Función para generar datos válidos
# Esta función genera datos aleatorios que cumplen con las restricciones del problema
generate_valid_data <- function(max_attempts = 1000) {
  for (attempt in 1:max_attempts) {
    # Generar números aleatorios para el total de personas y las intersecciones
    n_total <- sample(30:50, 1)  # Total de personas entrevistadas
    n_A <- sample(15:25, 1)      # Personas que escuchan género A
    n_B <- sample(15:25, 1)      # Personas que escuchan género B
    n_C <- sample(20:30, 1)      # Personas que escuchan género C

    # Calcular intersecciones entre los conjuntos
    n_AB <- sample(3:8, 1)       # Personas que escuchan A y B
    n_AC <- sample(5:10, 1)      # Personas que escuchan A y C
    n_BC <- sample(3:8, 1)       # Personas que escuchan B y C
    n_all <- sample(2:5, 1)      # Personas que escuchan A, B y C

    # Calcular valores para cada región del diagrama de Venn
    n_only_A <- n_A - n_AB - n_AC + n_all  # Solo escuchan A
    n_only_B <- n_B - n_AB - n_BC + n_all  # Solo escuchan B
    n_only_C <- n_C - n_AC - n_BC + n_all  # Solo escuchan C
    n_AB_only <- n_AB - n_all              # Solo escuchan A y B
    n_AC_only <- n_AC - n_all              # Solo escuchan A y C
    n_BC_only <- n_BC - n_all              # Solo escuchan B y C

    # Calcular el número de personas que no escuchan ninguno
    n_none <- n_total - (n_A + n_B + n_C - n_AB - n_AC - n_BC + n_all)

    # Verificar que todos los valores sean no negativos
    if (all(c(n_only_A, n_only_B, n_only_C, n_AB_only, n_AC_only, n_BC_only, n_all, n_none) >= 0)) {
      # Devolver los valores generados
      return(list(
        n_total = n_total, n_A = n_A, n_B = n_B, n_C = n_C,
        n_AB = n_AB, n_AC = n_AC, n_BC = n_BC, n_all = n_all,
        n_only_A = n_only_A, n_only_B = n_only_B, n_only_C = n_only_C,
        n_AB_only = n_AB_only, n_AC_only = n_AC_only, n_BC_only = n_BC_only,
        n_none = n_none
      ))
    }
  }
  stop("No se pudieron generar datos válidos después de ", max_attempts, " intentos")
}

# Función para generar géneros musicales aleatorios
# Esta función selecciona aleatoriamente tres géneros musicales de una lista predefinida
generate_random_genres <- function() {
  genres <- c("Vallenato", "Salsa", "Merengue", "Reggaeton", "Bachata", "Cumbia", "Rap", "Electrónica", "Tango", "Gospel")
  sample(genres, 3)
}

# Función para generar texto aleatorio con números consistentes
# Esta función crea un texto descriptivo basado en los datos generados
generate_random_text <- function(data, A, B, C) {
  templates <- c(
    "- %d de ellos escuchan %s y %s,\n- %d escuchan %s y %s, y\n- %d %s y %s.",
    "- %d de ellos escuchan solamente %s y %s,\n- %d escuchan %s y %s, y\n- %d %s y %s.",
    "- %d de ellos escuchan %s y %s,\n- %d solo escuchan %s y %s, y\n- %d %s y %s.",
    "- %d de ellos escuchan %s y %s,\n- %d escuchan %s y %s, y\n- %d solamente %s y %s."
  )

  template <- sample(templates, 1)
  values <- list(
    c(data$n_AB, data$n_AC, data$n_BC),
    c(data$n_AB_only, data$n_AC, data$n_BC),
    c(data$n_AB, data$n_AC_only, data$n_BC),
    c(data$n_AB, data$n_AC, data$n_BC_only)
  )
  chosen_values <- switch(which(templates == template),
                          values[[1]],
                          values[[2]],
                          values[[3]],
                          values[[4]])

  sprintf(
    "Se ha entrevistado a %d adolescentes sobre los géneros musicales que escuchan.\n\n- %d de ellos escuchan %s, \n- %d escuchan %s, y \n- %d %s. \n\nAdemás sabemos que \n\n%s",
    data$n_total, data$n_A, A, data$n_B, B, data$n_C, C,
    sprintf(template, chosen_values[1], A, B, chosen_values[2], A, C, chosen_values[3], B, C)
  )
}

# Generar datos válidos
tryCatch({
  data <- generate_valid_data()
}, error = function(e) {
  stop("Error al generar datos válidos: ", e$message)
})

# Asignar valores a variables individuales
for (name in names(data)) {
  assign(name, data[[name]])
}

# Crear géneros musicales aleatorios
genres <- generate_random_genres()
A <- genres[1]
B <- genres[2]
C <- genres[3]

# Generar texto aleatorio con números consistentes
random_text <- generate_random_text(data, A, B, C)

# Lista de preguntas
questions <- c(
  sprintf("¿Cuántas personas escuchan solamente %s y %s?", A, B),
  sprintf("¿Cuántos adolescentes disfrutan únicamente de %s y %s?", A, C),
  sprintf("¿Cuál es el número de entrevistados que solo escuchan %s y %s?", B, C),
  "¿Cuántas personas escuchan los tres géneros musicales?",
  "¿Cuántos adolescentes no escuchan ninguno de estos géneros musicales?",
  "¿Cuál es el total de personas que escuchan solo uno de estos géneros musicales?",
  "¿Cuántos entrevistados disfrutan de al menos dos géneros musicales?",
  "¿Cuántas personas escuchan exactamente dos géneros musicales?",
  sprintf("¿Cuál es el número de adolescentes que escuchan %s o %s, pero no ambos?", A, B),
  sprintf("¿Cuántos jóvenes escuchan %s pero no %s?", A, B),
  sprintf("¿Cuántas personas disfrutan de %s o %s, o ambos?", A, C),
  sprintf("¿Cuál es el número de entrevistados que no escuchan %s ni %s?", B, C),
  "¿Cuántos adolescentes escuchan al menos uno de estos géneros musicales?",
  "¿Cuántas personas escuchan exactamente un género musical?",
  "¿Cuál es el total de personas que escuchan máximo dos géneros musicales?",
  "¿Cuál es el total de personas que escuchan máximo tres géneros musicales?"
)

# Respuestas correspondientes a las preguntas
answers <- c(
  n_AB_only,
  n_AC_only,
  n_BC_only,
  n_all,
  n_none,
  n_only_A + n_only_B + n_only_C,
  n_AB + n_AC + n_BC - 2*n_all,
  n_AB_only + n_AC_only + n_BC_only,
  n_only_A + n_only_B,
  n_only_A + n_AC_only,
  n_A + n_C - n_AC,
  n_total - (n_B + n_C - n_BC),
  n_total - n_none,
  n_only_A + n_only_B + n_only_C,
  n_only_A + n_only_B + n_only_C + n_AB_only + n_AC_only + n_BC_only,
  n_total - n_none
)

# Seleccionar 4 preguntas aleatorias
selected_questions <- sample(questions, 4)

# Seleccionar las respuestas correspondientes a las preguntas seleccionadas
selected_answers <- sapply(seq_along(selected_questions), function(i) {
  answers[which(questions == selected_questions[i])]
})

# Usar el mismo tipo de gráfico (pdf, svg, png) que la llamada actual de xweave()
typ <- match_exams_device()

# Código TikZ para el Diagrama de Venn
tikz_venn <- sprintf('
\\begin{tikzpicture}[scale=0.7]
  \\begin{scope}[opacity=0.5]
    \\fill[red]   ( 90:1.5) circle (2);
    \\fill[green] (210:1.5) circle (2);
    \\fill[blue]  (330:1.5) circle (2);
  \\end{scope}
  \\node at ( 90:3.3) {%s};
  \\node at (210:3.3) {%s};
  \\node at (330:3.3) {%s};
  \\node at ( 90:1.3) {%d};
  \\node at (210:1.3) {%d};
  \\node at (330:1.3) {%d};
  \\node at ( 30:1.7) {%d};
  \\node at (150:1.7) {%d};
  \\node at (270:1.7) {%d};
  \\node at (0:0) {%d};
  \\node at (270:3.5) {Ninguno: %d};
\\end{tikzpicture}
', A, B, C, n_only_A, n_only_B, n_only_C, n_AC_only, n_AB_only, n_BC_only, n_all, n_none)

# Configuración de knitr para TikZ
knitr::opts_chunk$set(warning = FALSE, message = FALSE)

# Función para incluir el diagrama TikZ
include_venn <- function() {
  include_tikz(tikz_venn, name = "venn_diagram", markup = "markdown",
               format = typ, library = c("shapes", "backgrounds"),
               packages = c("tikz"),
               width = "6cm")
}
```

Question
========
`r random_text`

Por último sabemos que solamente `r n_all` escuchan los tres géneros musicales.

Considerando esa información:

Answerlist
----------
* `r selected_questions[1]`
* `r selected_questions[2]`
* `r selected_questions[3]`
* `r selected_questions[4]`

Solution
========
Para resolver este problema, analizamos el diagrama de Venn que representa la intersección de los tres conjuntos: `r A`, `r B` y `r C`.

```{r venn-diagram-solution, echo=FALSE, results="asis", fig.align="center", fig.width=6, fig.height=6}
include_venn()
```

Explicación detallada:

a) `r selected_questions[1]`: `r selected_answers[1]`
b) `r selected_questions[2]`: `r selected_answers[2]`
c) `r selected_questions[3]`: `r selected_answers[3]`
d) `r selected_questions[4]`: `r selected_answers[4]`

Answerlist
----------
* La respuesta es `r selected_answers[1]`.
* La respuesta es `r selected_answers[2]`.
* La respuesta es `r selected_answers[3]`.
* La respuesta es `r selected_answers[4]`.

Meta-information
================
exname: Géneros musicales
extype: cloze
exclozetype: num|num|num|num
exsolution: `r selected_answers[1]`|`r selected_answers[2]`|`r selected_answers[3]`|`r selected_answers[4]`
extol: 0
exsection: Diagrama de Venn