---
output:
  word_document: default
  html_document: default
  pdf_document: default
---

```{r setup, include=FALSE}
library(exams)
knitr::opts_chunk$set(echo = TRUE)
```

```{r data generation, echo = FALSE, results = "hide"}
# Generación de datos aleatorios
set.seed(NULL)  # Asegura aleatoriedad en cada ejecución

# Tamaño del conjunto de datos (entre 7 y 12 valores)
n <- sample(7:12, 1)

# Generar datos con distribución que tenga una moda definida
# Generamos valores enteros entre 1 y 20
datos_base <- sample(1:20, n-1, replace = TRUE)

# Aseguramos que haya un valor que se repita (la moda)
moda_valor <- sample(1:20, 1)
# Determinar cuántas veces aparecerá la moda (entre 2 y 3 veces)
moda_repeticiones <- sample(2:3, 1)

# Combinar los datos base con las repeticiones de la moda
datos <- c(datos_base, rep(moda_valor, moda_repeticiones))
# Mezclar el conjunto de datos
datos <- sample(datos, length(datos))

# Calcular la media, mediana y moda
media <- mean(datos)
mediana <- median(datos)

# Función para encontrar la moda
encontrar_moda <- function(x) {
  tabla <- table(x)
  modas <- as.numeric(names(tabla[tabla == max(tabla)]))
  if (length(modas) > 1) {
    # Selecciona una moda al azar si hay múltiples
    moda <- sample(modas, 1)
  } else {
    moda <- modas[1]
  }
  return(moda)
}

moda <- encontrar_moda(datos)

# Asegurar que los tres valores sean distintos para mayor claridad
while (length(unique(c(round(media, 2), mediana, moda))) < 3) {
  # Regenerar datos si la media, mediana y moda son iguales
  datos_base <- sample(1:20, n-1, replace = TRUE)
  moda_valor <- sample(1:20, 1)
  moda_repeticiones <- sample(2:3, 1)
  datos <- c(datos_base, rep(moda_valor, moda_repeticiones))
  datos <- sample(datos, length(datos))
  
  media <- mean(datos)
  mediana <- median(datos)
  moda <- encontrar_moda(datos)
}

# Verificar si hay más de una moda
tabla_frec <- table(datos)
max_freq <- max(tabla_frec)
modas <- as.numeric(names(tabla_frec[tabla_frec == max_freq]))

while (length(modas) > 1) {
  # Regenerar datos si hay más de una moda
  datos_base <- sample(1:20, n-1, replace = TRUE)
  moda_valor <- sample(1:20, 1)
  moda_repeticiones <- sample(2:3, 1)
  datos <- c(datos_base, rep(moda_valor, moda_repeticiones))
  datos <- sample(datos, length(datos))
  
  media <- mean(datos)
  mediana <- median(datos)
  moda <- encontrar_moda(datos)

  tabla_frec <- table(datos)
  max_freq <- max(tabla_frec)
  modas <- as.numeric(names(tabla_frec[tabla_frec == max_freq]))
}

# Redondear la media a 2 decimales para la solución
media_redondeada <- round(media, 2)

# Soluciones para el formato cloze
sol_media <- as.character(media_redondeada)
sol_mediana <- as.character(mediana)
sol_moda <- as.character(moda)

# Tolerancia para la respuesta numérica de la media
tol_media <- 0.01
```

Question
========

En un estudio sobre rendimiento académico universitario, se registraron las siguientes calificaciones obtenidas por estudiantes en la prueba de matemáticas:

$$`r paste(datos, collapse=", ")`$$

Complete la siguiente tabla con los valores correspondientes a las medidas de tendencia central (con dos decimales para la media si es necesario):

```{=html}
<table border="1" style="border-collapse: collapse; width: 50%;">
  <tr>
    <th style="padding: 8px; text-align: center;">Medida</th>
    <th style="padding: 8px; text-align: center;">Valor</th>
  </tr>
  <tr>
    <td style="padding: 8px;">Media</td>
    <td style="padding: 8px; text-align: center;">##ANSWER1##</td>
  </tr>
  <tr>
    <td style="padding: 8px;">Mediana</td>
    <td style="padding: 8px; text-align: center;">##ANSWER2##</td>
  </tr>
  <tr>
    <td style="padding: 8px;">Moda</td>
    <td style="padding: 8px; text-align: center;">##ANSWER3##</td>
  </tr>
</table>
```

Solution
========

Para este conjunto de datos: $`r paste(datos, collapse=", ")`$

**Media**:
La media se calcula sumando todos los valores y dividiendo por el número total de valores.

$$\text{Media} = \frac{`r paste(datos, collapse=" + ")`}{`r length(datos)`} = \frac{`r sum(datos)`}{`r length(datos)`} = `r media_redondeada`$$

**Mediana**:
La mediana es el valor central cuando los datos están ordenados.

Datos ordenados: $`r paste(sort(datos), collapse=", ")`$

Como hay `r length(datos)` valores (número `r ifelse(length(datos) %% 2 == 0, "par", "impar")`), la mediana es `r if(length(datos) %% 2 == 0) paste("el promedio de los dos valores centrales:", (sort(datos)[length(datos)/2] + sort(datos)[length(datos)/2 + 1])/2) else paste("el valor central:", sort(datos)[ceiling(length(datos)/2)])`.

$$\text{Mediana} = `r mediana`$$

**Moda**:
La moda es el valor que aparece con mayor frecuencia.

Frecuencias de cada valor:
```{r, echo=FALSE, results='asis'}
tabla_frec <- table(datos)
for(i in 1:length(tabla_frec)) {
  cat(paste0("El valor ", names(tabla_frec)[i], " aparece ", tabla_frec[i], " veces\n\n"))
}
```

El valor `r moda` aparece con mayor frecuencia (`r max(table(datos))` veces), por lo tanto:

$$\text{Moda} = `r moda`$$

Meta-information
================
extype: cloze
exsolution: `r sol_media`|`r sol_mediana`|`r sol_moda`
exclozetype: num|num|num
exname: Media Mediana Moda
extol: `r tol_media`|0|0