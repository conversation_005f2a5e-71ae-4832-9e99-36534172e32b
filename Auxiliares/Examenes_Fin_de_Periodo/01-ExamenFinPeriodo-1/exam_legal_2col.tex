\documentclass[10pt]{article}

%% packages
\usepackage[papersize={216mm,356mm},tmargin=5mm,bmargin=5mm,lmargin=5mm,rmargin=5mm]{geometry}
\usepackage[utf8]{inputenc}
\usepackage{amsmath,amssymb,amsfonts,latexsym,cancel,amsthm}
\usepackage{a4wide,color,verbatim,Sweave,url,xargs,amsmath,booktabs,longtable,eurosym}
\usepackage{tikz}
\usepackage{pgfplots}
\usepackage{multicol}
\usepackage{graphicx}
\usepackage[spanish]{babel}
\usepackage{floatrow}
\usepackage{hyperref}

%% new environments
\newenvironment{question}{\item}{}
\newenvironment{solution}{\comment}{\endcomment}
\newenvironment{answerlist}{\renewcommand{\labelenumii}{(\alph{enumii})}\begin{enumerate}}{\end{enumerate}}

%% paragraphs
\setlength{\parskip}{0.5ex plus0.1ex minus0.1ex}
\setlength{\parindent}{0em}

%% compatibility with pandoc
\providecommand{\tightlist}{\setlength{\itemsep}{0pt}\setlength{\parskip}{0pt}}
\providecommand{\pandocbounded}[1]{#1}
\setkeys{Gin}{keepaspectratio}

% Configuración para doble columna con línea separadora
\setlength{\columnseprule}{0.4pt}

% Ajustes para centrar figuras y tablas en entorno de doble columna
\usepackage{etoolbox}
\AtBeginEnvironment{figure}{\centering}
\AtBeginEnvironment{table}{\centering}

% Ajustes para figuras de ancho completo en doble columna
\usepackage{caption}
\usepackage{newfloat}
\usepackage{wrapfig}
\usepackage{capt-of}

% Definir un nuevo tipo de figura que ocupa ambas columnas
\DeclareFloatingEnvironment[
  fileext=lof,
  listname={Lista de figuras},
  name=Figura,
  placement=tbhp,
  within=section,
]{widefigure}

% Definir un nuevo tipo de tabla que ocupa ambas columnas
\DeclareFloatingEnvironment[
  fileext=lot,
  listname={Lista de tablas},
  name=Tabla,
  placement=tbhp,
  within=section,
]{widetable}

% Añadir soporte para figuras de ancho completo a través de columnas
\usepackage{float}
\floatstyle{plaintop}
\restylefloat{figure}
\restylefloat{table}

% Entorno para figuras de ancho completo
\newenvironment{widefigureenv}{%
  \begin{figure*}[!ht]
    \centering
}{%
  \end{figure*}
}

% Entorno para tablas de ancho completo
\newenvironment{widetableenv}{%
  \begin{table*}[!ht]
    \centering
}{%
  \end{table*}
}

%% fonts: Helvetica
\usepackage{helvet}
\IfFileExists{sfmath.sty}{
  \RequirePackage[helvet]{sfmath}
}{}
\renewcommand{\sfdefault}{phv}
\renewcommand{\rmdefault}{phv}

%% commands for exams package
\newcommand{\exmchoice}[6]{%
  \ifx#1\empty\else #1: \fi
  \ifx#2\empty\else (a) #2 \fi
  \ifx#3\empty\else (b) #3 \fi
  \ifx#4\empty\else (c) #4 \fi
  \ifx#5\empty\else (d) #5 \fi
  \ifx#6\empty\else (e) #6 \fi
}
\newcommand{\extext}[1]{#1}
\newcommand{\exsolution}[1]{\textbf{Solución:} #1}
\newcommand{\exstring}[1]{%
  \mbox{\framebox[0.9\textwidth][l]{\rule[-1mm]{0mm}{5mm} \hspace*{-1.6mm} \extext{#1}} \hspace*{2mm}}%
}
\newcommand{\exnum}[9]{%
  \mbox{\framebox[8mm]{\rule[-1mm]{0mm}{5mm} \hspace*{-1.6mm} \extext{#1}}}%
  \mbox{\framebox[8mm]{\rule[-1mm]{0mm}{5mm} \hspace*{-1.6mm} \extext{#2}}}%
  \mbox{\framebox[8mm]{\rule[-1mm]{0mm}{5mm} \hspace*{-1.6mm} \extext{#3}}}%
  \mbox{\framebox[8mm]{\rule[-1mm]{0mm}{5mm} \hspace*{-1.6mm} \extext{#4}}}%
  \mbox{\framebox[8mm]{\rule[-1mm]{0mm}{5mm} \hspace*{-1.6mm} \extext{#5}}}%
  \mbox{\framebox[8mm]{\rule[-1mm]{0mm}{5mm} \hspace*{-1.6mm} \extext{#6}}}%
  \mbox{ \makebox[3mm]{\rule[-1mm]{0mm}{5mm} \hspace*{-2mm} .}}%
  \mbox{\framebox[8mm]{\rule[-1mm]{0mm}{5mm} \hspace*{-1.6mm} \extext{#7}}}%
  \mbox{\framebox[8mm]{\rule[-1mm]{0mm}{5mm} \hspace*{-1.6mm} \extext{#8}}}%
  \mbox{\framebox[8mm]{\rule[-1mm]{0mm}{5mm} \hspace*{-1.6mm} \extext{#9}}}%
}

%% new commands
\makeatletter
\newcommand{\ID}[1]{\def\@ID{#1}}
\newcommand{\Date}[1]{\def\@Date{#1}}
\ID{00001}
\Date{YYYY-MM-DD}

%% \exinput{header}

\newcommand{\myID}{\@ID}
\newcommand{\myDate}{\@Date}
\makeatother

%% headings
\markboth{\textnormal{\bf \large Examen de Matemáticas: \myID}}%
{\textnormal{\bf \large Examen de Matemáticas: \myID}}
\pagestyle{myheadings}

% Enfoque completamente diferente: usar un paquete especial para tablas en modo multicolumna
\usepackage{array}
\usepackage{colortbl}
\usepackage{multirow}

% Hacer que los comandos específicos de longtable no hagan nada
\newcommand{\endhead}{}
\newcommand{\endfirsthead}{}
\newcommand{\endfoot}{}
\newcommand{\endlastfoot}{}

% Redefinir longtable para usar tabular directamente
\renewenvironment{longtable}[1]{%
  \begin{table}[htbp]
  \centering
  \begin{tabular}{ccc}
}{%
  \end{tabular}
  \end{table}
}

\begin{document}

%% title page
\thispagestyle{empty}
{\sf
\textbf{\LARGE{Institución Educativa Pedacito de Cielo}}

\textbf{\large{Matemáticas \myDate \hfill Taller ID \myID}}

\vspace*{1.5cm}

\begin{tabular}{p{14cm}}
\textbf{Apellidos y Nombres:} \hrule \\[1.2cm]
\textbf{No. Documento de Identidad:} \hrule \\[1.2cm]
\textbf{Fecha:} \hrule  \\[1.2cm]
\end{tabular}

\vspace*{0.5cm}

%% \exinput{questionnaire}
}
\newpage

% Iniciar el entorno de doble columna
\begin{multicols}{2}

\begin{enumerate}

%% \exinput{exercises}

\end{enumerate}

\end{multicols}
\end{document}
