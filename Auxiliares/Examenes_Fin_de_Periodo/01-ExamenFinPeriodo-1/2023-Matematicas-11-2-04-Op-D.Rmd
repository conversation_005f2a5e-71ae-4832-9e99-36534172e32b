---
output:
  word_document: default
  pdf_document: default
  html_document: default
---
```{r data generation, echo = FALSE, results = "hide"}
# Generación de datos aleatorios
library(exams)

# Generación de nombres aleatorios para modelos
modelos <- c("X", "Y", "Z")
modelos <- sample(c("Modelo A", "Modelo B", "Modelo C", "Modelo D", "Modelo E", 
                    "Modelo F", "Modelo G", "Modelo H", "Modelo J", "Modelo K", 
                    "Modelo L", "Modelo M", "Modelo N", "Modelo P", "Modelo Q", 
                    "Modelo R", "Modelo S", "Modelo T", "Modelo U", "Modelo V", 
                    "Modelo W"), 3)

# Generación de datos base para cada modelo
# Aseguramos que las medianas tengan la relación deseada
base_valores <- sample(500:800, 15, replace = TRUE)
matriz_datos <- matrix(base_valores, nrow = 3, ncol = 5)

# Ajustamos los valores para que el modelo Z tenga la mediana mayor que X y menor que Y
mediana_deseada_x <- median(sort(matriz_datos[1,]))

# Aseguramos que Y tenga una mediana mayor
matriz_datos[2,] <- matriz_datos[2,] + 50
mediana_deseada_y <- median(sort(matriz_datos[2,]))

while (mediana_deseada_y <= mediana_deseada_x + 30) {
  matriz_datos[2,] <- matriz_datos[2,] + 10
  mediana_deseada_y <- median(sort(matriz_datos[2,]))
}

# Aseguramos que Z tenga una mediana mayor que X pero menor que Y
matriz_datos[3,] <- sort(matriz_datos[1,]) + 20
mediana_deseada_z <- median(sort(matriz_datos[3,]))

while (mediana_deseada_z <= mediana_deseada_x || mediana_deseada_z >= mediana_deseada_y) {
  if (mediana_deseada_z <= mediana_deseada_x) {
    matriz_datos[3,] <- matriz_datos[3,] + 10
  } else if (mediana_deseada_z >= mediana_deseada_y) {
    matriz_datos[3,] <- matriz_datos[3,] - 10
  }
  mediana_deseada_z <- median(sort(matriz_datos[3,]))
}

# Garantizamos que haya dos valores iguales en dos modelos diferentes
# Elegimos aleatoriamente qué modelos tendrán un valor igual
modelos_con_valor_igual <- sample(1:3, 2)
posicion_valor_igual <- sample(1:5, 1)
valor_comun <- matriz_datos[modelos_con_valor_igual[1], posicion_valor_igual]
matriz_datos[modelos_con_valor_igual[2], posicion_valor_igual] <- valor_comun

# Verificamos que las medianas sigan manteniendo la relación deseada
mediana_x <- median(matriz_datos[1,])
mediana_y <- median(matriz_datos[2,])
mediana_z <- median(matriz_datos[3,])

# Si las medianas no mantienen la relación deseada, ajustamos
while (!(mediana_z > mediana_x && mediana_z < mediana_y)) {
  if (mediana_z <= mediana_x) {
    # Incrementamos un valor en Z que no sea el valor común
    posiciones_no_comunes <- setdiff(1:5, posicion_valor_igual)
    pos_a_cambiar <- ifelse(modelos_con_valor_igual[2] == 3, 
                           sample(posiciones_no_comunes, 1), 
                           posicion_valor_igual)
    matriz_datos[3, pos_a_cambiar] <- matriz_datos[3, pos_a_cambiar] + 20
  } else if (mediana_z >= mediana_y) {
    # Decrementamos un valor en Z que no sea el valor común
    posiciones_no_comunes <- setdiff(1:5, posicion_valor_igual)
    pos_a_cambiar <- ifelse(modelos_con_valor_igual[2] == 3, 
                           sample(posiciones_no_comunes, 1), 
                           posicion_valor_igual)
    matriz_datos[3, pos_a_cambiar] <- matriz_datos[3, pos_a_cambiar] - 20
  }
  
  mediana_z <- median(matriz_datos[3,])
}

# Creamos la tabla de datos
tabla <- data.frame(
  Prueba = paste("Prueba", 1:5),
  Modelo1 = matriz_datos[1,],
  Modelo2 = matriz_datos[2,],
  Modelo3 = matriz_datos[3,]
)

# Calculamos las medianas para verificación
med1 <- median(matriz_datos[1,])
med2 <- median(matriz_datos[2,])
med3 <- median(matriz_datos[3,])

# Generamos nombres aleatorios para el contexto
marcas <- c("SmartCell", "TechLife", "PowerMobile", "CellTech", "MobilPro")
marca_seleccionada <- sample(marcas, 1)
pruebas <- c("rendimiento", "uso intensivo", "resistencia", "durabilidad", "eficiencia")
prueba_seleccionada <- sample(pruebas, 1)
```

Question
========
La empresa `r marca_seleccionada` realizó pruebas de `r prueba_seleccionada` en tres modelos de teléfonos celulares. La siguiente tabla muestra la duración de la batería (en minutos) para cada modelo durante cinco pruebas diferentes:

```{r, echo = FALSE, results = "asis"}
# Crear un vector de índices aleatorios para reordenar los modelos
indices_aleatorios <- sample(1:3)

# Crear un vector con los nombres de modelos reordenados
modelos_aleatorios <- modelos[indices_aleatorios]

# Crear una nueva tabla con las columnas reordenadas para mantener la correspondencia
# La primera columna (Prueba) siempre se mantiene en su posición
tabla_reordenada <- tabla[, c(1, indices_aleatorios + 1)]

# Generar la tabla con los nombres de columnas aleatorizados
# pero manteniendo la correspondencia con los datos
knitr::kable(tabla_reordenada, col.names = c("", modelos_aleatorios))

# Guardar la correspondencia entre los índices originales y los aleatorios
# para usarla en el resto del documento
indice_x <- which(indices_aleatorios == 1)
indice_y <- which(indices_aleatorios == 2)
indice_z <- which(indices_aleatorios == 3)
```

Al comparar la mediana de las duraciones de la batería de los tres modelos, ¿cuál de las siguientes afirmaciones es correcta?

Answerlist
----------
* El `r modelos_aleatorios[indice_z]` tiene una mediana igual a la del `r modelos_aleatorios[indice_y]` y menor que la del `r modelos_aleatorios[indice_x]`.
* El `r modelos_aleatorios[indice_z]` tiene una mediana mayor que la del `r modelos_aleatorios[indice_y]` y que la del `r modelos_aleatorios[indice_x]`.
* El `r modelos_aleatorios[indice_z]` tiene una mediana igual a la del `r modelos_aleatorios[indice_x]` y menor que la del `r modelos_aleatorios[indice_y]`.
* El `r modelos_aleatorios[indice_z]` tiene una mediana mayor que la del `r modelos_aleatorios[indice_x]` y menor que la del `r modelos_aleatorios[indice_y]`.

Solution
========
La respuesta correcta es: El `r modelos_aleatorios[indice_z]` tiene una mediana mayor que la del `r modelos_aleatorios[indice_x]` y menor que la del `r modelos_aleatorios[indice_y]`.

Para resolver este problema, debemos:

1. Ordenar los datos de cada modelo y encontrar su mediana:

* `r modelos_aleatorios[indice_x]`: `r sort(matriz_datos[1,])` → Mediana = `r med1`
* `r modelos_aleatorios[indice_y]`: `r sort(matriz_datos[2,])` → Mediana = `r med2`
* `r modelos_aleatorios[indice_z]`: `r sort(matriz_datos[3,])` → Mediana = `r med3`

2. Comparar las medianas:
* Mediana de `r modelos_aleatorios[indice_z]` (`r med3`) > Mediana de `r modelos_aleatorios[indice_x]` (`r med1`)
* Mediana de `r modelos_aleatorios[indice_z]` (`r med3`) < Mediana de `r modelos_aleatorios[indice_y]` (`r med2`)

Por lo tanto, el `r modelos_aleatorios[indice_z]` tiene una mediana mayor que la del `r modelos_aleatorios[indice_x]` y menor que la del `r modelos_aleatorios[indice_y]`.

Meta-information
============
exname: medianas-celulares
extype: schoice
exsolution: 0001
exshuffle: TRUE