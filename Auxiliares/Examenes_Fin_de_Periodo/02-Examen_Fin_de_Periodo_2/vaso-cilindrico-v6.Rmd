---
output:
  html_document: default
  pdf_document:
    latex_engine: pdflatex
  word_document: default
header-includes:
- \usepackage[spanish]{babel}
- \usepackage{amsmath}
- \usepackage{tikz}
---

```{r data_generation, echo=FALSE, results="hide"}
# Cargar librerías necesarias
library(exams)
library(knitr)

# Establecer semilla aleatoria para reproducibilidad
set.seed(sample(1:1000, 1))

# Aleatorizar nombres para el contexto
nombres <- c("<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>",
             "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>",
             "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>")
nombre <- sample(nombres, 1)

# Aleatorizar dimensiones del cilindro
radio <- sample(20:50, 1)  # Radio entre 20 y 50 cm
altura <- sample(60:100, 1)  # Altura entre 60 y 100 cm

# Calcular el volumen del cilindro
volumen <- pi * radio^2 * altura
# Formatear el volumen para evitar notación científica
volumen_redondeado <- format(round(volumen, 2),
                             scientific = FALSE,
                             big.mark = "\\,")
# Eliminar espacios en blanco al inicio y final si existen
volumen_redondeado <- trimws(volumen_redondeado)

# Calcular otras medidas para las opciones
area_base <- pi * radio^2
area_base_redondeada <- format(round(area_base, 2),
                               scientific = FALSE,
                               big.mark = "\\,")
area_base_redondeada <- trimws(area_base_redondeada)

perimetro_base <- 2 * pi * radio
perimetro_base_redondeado <- format(round(perimetro_base, 2),
                                    scientific = FALSE,
                                    big.mark = "\\,")
perimetro_base_redondeado <- trimws(perimetro_base_redondeado)

area_lateral <- 2 * pi * radio * altura
area_lateral_redondeada <- format(round(area_lateral, 2),
                                  scientific = FALSE,
                                  big.mark = "\\,")
area_lateral_redondeada <- trimws(area_lateral_redondeada)

# Calcular el área total (área lateral + 2 bases)
area_total <- area_lateral + 2 * area_base
area_total_redondeada <- format(round(area_total, 2),
                                scientific = FALSE,
                                big.mark = "\\,")
area_total_redondeada <- trimws(area_total_redondeada)

# Crear vector de opciones común
opciones <- c(
  paste("Al volumen del vaso"),
  paste("Al área de la tapa del vaso"),
  paste("Al perímetro de la tapa del vaso"),
  paste("Al área lateral del vaso")
)

# Seleccionar aleatoriamente una de las 5 versiones
version <- sample(1:5, 1)

# Ajustar opciones y solución según la versión
if (version == 5) {
  # Para la versión 5, cambiamos la opción 4 a área total
  opciones[4] <- paste("Al área total del vaso (incluyendo todas sus caras)")
}

# Establecer la respuesta correcta según la versión
if (version == 1) {
  # Versión 1: Volumen
  solucion <- 1
  formula_texto <- paste0("\\pi \\times ", radio, "^2 \\times ", altura, " = ", volumen_redondeado)
  formula_tikz <- paste0("$\\pi \\times ", radio, "^2 \\times ", altura, " = ", volumen_redondeado, "$")
  exname <- "Volumen_Cilindro"
} else if (version == 2) {
  # Versión 2: Área lateral
  solucion <- 4
  formula_texto <- paste0("2 \\times \\pi \\times ", radio, " \\times ", altura, " = ", area_lateral_redondeada)
  formula_tikz <- paste0("$2 \\times \\pi \\times ", radio, " \\times ", altura, " = ", area_lateral_redondeada, "$")
  exname <- "Area_Lateral_Cilindro"
} else if (version == 3) {
  # Versión 3: Perímetro de la tapa
  solucion <- 3
  formula_texto <- paste0("2 \\times \\pi \\times ", radio, " = ", perimetro_base_redondeado)
  formula_tikz <- paste0("$2 \\times \\pi \\times ", radio, " = ", perimetro_base_redondeado, "$")
  exname <- "Perimetro_Tapa_Cilindro"
} else if (version == 4) {
  # Versión 4: Área de la tapa
  solucion <- 2
  formula_texto <- paste0("\\pi \\times ", radio, "^2 = ", area_base_redondeada)
  formula_tikz <- paste0("$\\pi \\times ", radio, "^2 = ", area_base_redondeada, "$")
  exname <- "Area_Tapa_Cilindro"
} else {
  # Versión 5: Área total
  solucion <- 4
  formula_texto <- paste0("2 \\times \\pi \\times ", radio, " \\times ", altura, " + 2 \\times \\pi \\times ", radio, "^2 = ", area_total_redondeada)
  formula_tikz <- paste0("$2 \\times \\pi \\times ", radio, " \\times ", altura, " + 2 \\times \\pi \\times ", radio, "^2 = ", area_total_redondeada, "$")
  exname <- "Area_Total_Cilindro"
}

# Mezclar las opciones
indices_mezclados <- sample(1:4)
opciones_mezcladas <- opciones[indices_mezclados]
solucion_mezclada <- which(indices_mezclados == solucion)

# Crear vector de solución para exams
sol <- rep(0, 4)
sol[solucion_mezclada] <- 1

# Definir el contexto según la versión
if (version == 5) {
  contexto <- paste0(nombre, " está diseñando un recipiente cilíndrico para un proyecto escolar")
} else {
  contexto <- "Un estudiante tiene un vaso de forma cilíndrica"
}
```

```{r, echo=FALSE}
# Código para generar el cilindro con TikZ
# Definir factor de escala para mantener proporciones con valores grandes
factor_escala <- 0.1
radio_escalado <- radio * factor_escala
altura_escalada <- altura * factor_escala

# Base del código TikZ común para todas las versiones
tikz_base <- paste0("\\begin{tikzpicture}[>=latex]
    % Definir la relación de aspecto para la perspectiva
    \\def\\relacionAspecto{0.4} % Relación para la elipse

    % Dibujar la parte trasera del cilindro (líneas punteadas)
    \\draw[dashed] (-", radio_escalado, ",0) --
        (-", radio_escalado, ",", altura_escalada, ");
    \\draw[dashed] (0,0) ellipse
        (", radio_escalado, " and ", radio_escalado, "*\\relacionAspecto);

    % Sombrear el cilindro para dar efecto 3D
    \\fill[left color=blue!10, right color=blue!30, opacity=0.3]
        (-", radio_escalado, ",0)
        arc(180:360:", radio_escalado, " and ",
            radio_escalado, "*\\relacionAspecto) --
        (", radio_escalado, ",", altura_escalada, ")
        arc(0:-180:", radio_escalado, " and ",
            radio_escalado, "*\\relacionAspecto) --
        cycle;")

# Añadir sombreado especial para la versión 5 (área total)
if (version == 5) {
  tikz_sombreado <- paste0("
    % Sombrear la base superior para destacarla
    \\fill[blue!20, opacity=0.5]
        (0,", altura_escalada, ") ellipse
        (", radio_escalado, " and ", radio_escalado, "*\\relacionAspecto);
    
    % Sombrear la base inferior (visible parcialmente)
    \\fill[blue!10, opacity=0.3]
        (0,0) ellipse
        (", radio_escalado, " and ", radio_escalado, "*\\relacionAspecto);")
} else {
  tikz_sombreado <- ""
}

# Continuar con el resto del código TikZ común
tikz_resto <- paste0("
    % Dibujar la parte frontal del cilindro (líneas sólidas)
    \\draw (", radio_escalado, ",0) --
        (", radio_escalado, ",", altura_escalada, ");
    \\draw (0,", altura_escalada, ") ellipse
        (", radio_escalado, " and ", radio_escalado, "*\\relacionAspecto);

    % Añadir etiquetas y medidas
    \\draw[<->] (", radio_escalado + 0.5, ",0) --
        (", radio_escalado + 0.5, ",", altura_escalada, ")
        node[midway, right, font=\\large] {", altura, " cm};
    \\draw[<->] (0,", altura_escalada + 0.5, ") --
        (", radio_escalado, ",", altura_escalada + 0.5, ")
        node[midway, above, font=\\large] {", radio, " cm};

    % Añadir la operación matemática
    \\node[align=center, font=\\large] at (0,-2)
        {", formula_tikz, "};
\\end{tikzpicture}")

# Combinar todas las partes del código TikZ
cilindro_tikz <- paste0(tikz_base, tikz_sombreado, tikz_resto)
```

Question
========

`r contexto`. El vaso tiene una base circular de radio `r radio` cm y una altura de `r altura` cm, como se muestra en la figura.


```{r cilindro, echo=FALSE, results="asis", fig.align='center'}
include_tikz(cilindro_tikz,
             name = "cilindro",
             markup = "markdown",
             format = match_exams_device(),
             packages = c("tikz"),
             width = "8cm")
```

\
A partir de la información anterior, el estudiante plantea la siguiente operación:

$$`r formula_texto`$$

¿A qué corresponde el resultado de la anterior operación?

Answerlist
----------
* `r opciones_mezcladas[1]`
* `r opciones_mezcladas[2]`
* `r opciones_mezcladas[3]`
* `r opciones_mezcladas[4]`

Solution
========

```{r solution_generation, echo=FALSE, results="hide"}
# Generar la solución según la versión
if (version == 1) {
  # Solución para Volumen
  solucion_texto <- paste0("Para resolver este problema, debemos identificar qué cálculo se está realizando con la fórmula $\\pi \\times ", radio, "^2 \\times ", altura, " = ", volumen_redondeado, "$.

Analizando la fórmula:

- $\\pi \\times r^2$ corresponde al área de la base circular del cilindro
- Al multiplicar esta área por la altura $h$, obtenemos el volumen del cilindro

Por lo tanto, la fórmula $V = \\pi \\times r^2 \\times h$ es la fórmula del volumen de un cilindro.

Sustituyendo los valores:

- Radio de la base: $r = ", radio, " \\text{ cm}$
- Altura: $h = ", altura, " \\text{ cm}$

Calculamos:

$V = \\pi \\times (", radio, ")^2 \\times ", altura, " = \\pi \\times ", format(radio^2, scientific = FALSE), " \\times ", altura, " = ", volumen_redondeado, " \\text{ cm}^3$

El resultado corresponde al volumen del vaso cilíndrico.")
} else if (version == 2) {
  # Solución para Área lateral
  solucion_texto <- paste0("Para resolver este problema, debemos identificar qué cálculo se está realizando con la fórmula $2 \\times \\pi \\times ", radio, " \\times ", altura, " = ", area_lateral_redondeada, "$.

Analizando la fórmula:

- $2 \\times \\pi \\times r$ corresponde al perímetro de la base circular del cilindro
- Al multiplicar este perímetro por la altura $h$, obtenemos el área lateral del cilindro

Por lo tanto, la fórmula $A_L = 2 \\times \\pi \\times r \\times h$ es la fórmula del área lateral de un cilindro.

Sustituyendo los valores:

- Radio de la base: $r = ", radio, " \\text{ cm}$
- Altura: $h = ", altura, " \\text{ cm}$

Calculamos:

$A_L = 2 \\times \\pi \\times ", radio, " \\times ", altura, " = 2 \\times \\pi \\times ", radio, " \\times ", altura, " = ", area_lateral_redondeada, " \\text{ cm}^2$

El resultado corresponde al área lateral del vaso cilíndrico, que es la superficie rectangular que forma la pared del cilindro (sin incluir las bases).")
} else if (version == 3) {
  # Solución para Perímetro de la tapa
  solucion_texto <- paste0("Para resolver este problema, debemos identificar qué cálculo se está realizando con la fórmula $2 \\times \\pi \\times ", radio, " = ", perimetro_base_redondeado, "$.

Analizando la fórmula:

- $2 \\times \\pi \\times r$ corresponde al perímetro de un círculo con radio $r$

Por lo tanto, la fórmula $P = 2 \\times \\pi \\times r$ es la fórmula del perímetro de un círculo.

Sustituyendo los valores:

- Radio de la base: $r = ", radio, " \\text{ cm}$

Calculamos:

$P = 2 \\times \\pi \\times ", radio, " = ", perimetro_base_redondeado, " \\text{ cm}$

El resultado corresponde al perímetro de la tapa del vaso cilíndrico, que es la longitud de la circunferencia que forma el borde superior del vaso.")
} else if (version == 4) {
  # Solución para Área de la tapa
  solucion_texto <- paste0("Para resolver este problema, debemos identificar qué cálculo se está realizando con la fórmula $\\pi \\times ", radio, "^2 = ", area_base_redondeada, "$.

Analizando la fórmula:

- $\\pi \\times r^2$ corresponde al área de un círculo con radio $r$

Por lo tanto, la fórmula $A = \\pi \\times r^2$ es la fórmula del área de un círculo.

Sustituyendo los valores:

- Radio de la base: $r = ", radio, " \\text{ cm}$

Calculamos:

$A = \\pi \\times ", radio, "^2 = ", area_base_redondeada, " \\text{ cm}^2$

El resultado corresponde al área de la tapa del vaso cilíndrico, que es la superficie circular que forma la parte superior del vaso.")
} else {
  # Solución para Área total
  solucion_texto <- paste0("Para resolver este problema, debemos identificar qué cálculo se está realizando con la fórmula $2 \\times \\pi \\times ", radio, " \\times ", altura, " + 2 \\times \\pi \\times ", radio, "^2 = ", area_total_redondeada, "$.

Analizando la fórmula por partes:

1. $2 \\times \\pi \\times r \\times h$ corresponde al área lateral del cilindro
   - El perímetro de la base es $2 \\times \\pi \\times r$
   - Al multiplicar por la altura $h$, obtenemos el área lateral

2. $2 \\times \\pi \\times r^2$ corresponde al área de las dos bases circulares
   - El área de un círculo es $\\pi \\times r^2$
   - Al multiplicar por 2, obtenemos el área de ambas bases (superior e inferior)

3. La suma de ambas expresiones nos da el área total del cilindro (todas sus caras)

Por lo tanto, la fórmula $A_T = 2 \\times \\pi \\times r \\times h + 2 \\times \\pi \\times r^2$ es la fórmula del área total de un cilindro.

Sustituyendo los valores:
- Radio de la base: $r = ", radio, " \\text{ cm}$
- Altura: $h = ", altura, " \\text{ cm}$

Calculamos:
- Área lateral: $A_L = 2 \\times \\pi \\times ", radio, " \\times ", altura, " = ", area_lateral_redondeada, " \\text{ cm}^2$
- Área de las dos bases: $A_B = 2 \\times \\pi \\times ", radio, "^2 = 2 \\times ", area_base_redondeada, " = ", format(round(2*area_base, 2), scientific=FALSE, big.mark="\\,"), " \\text{ cm}^2$
- Área total: $A_T = A_L + A_B = ", area_lateral_redondeada, " + ", format(round(2*area_base, 2), scientific=FALSE, big.mark="\\,"), " = ", area_total_redondeada, " \\text{ cm}^2$

El resultado corresponde al área total del vaso cilíndrico, que representa la cantidad total de material necesario para construirlo, incluyendo la superficie lateral y las dos bases circulares.")
}
```

`r solucion_texto`

Answerlist
----------
* `r ifelse(sol[1] == 1, "Verdadero", "Falso")`
* `r ifelse(sol[2] == 1, "Verdadero", "Falso")`
* `r ifelse(sol[3] == 1, "Verdadero", "Falso")`
* `r ifelse(sol[4] == 1, "Verdadero", "Falso")`

Meta-information
================
exname: `r exname`
extype: schoice
exsolution: `r paste(sol, collapse="")`
exshuffle: TRUE
