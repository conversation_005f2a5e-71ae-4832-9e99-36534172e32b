---
output:
  html_document: default
  pdf_document:
    keep_tex: true
    extra_dependencies: ["graphicx", "float", "tikz", "xcolor"]
  word_document: default
icfes:
  competencia: Resolución de problemas
  componente: Numérico-variacional
  afirmacion: Resuelve problemas que requieren el uso de fracciones y porcentajes
  evidencia: Utiliza fracciones para resolver problemas de reparto proporcional
  nivel: Medio
  tematica: Fracciones y operaciones con fracciones
---
```{r setup, include=FALSE}
# Configuración para todos los formatos de salida
Sys.setlocale(category = "LC_NUMERIC", locale = "C")
options(OutDec = ".")

# Configurar el motor LaTeX globalmente
options(tikzLatex = "pdflatex")
options(tikzXelatex = FALSE)
options(tikzLatexPackages = c(
  "\\usepackage{tikz}",
  "\\usepackage{colortbl}",
  "\\usepackage{xcolor}",
  "\\usepackage{graphicx}",
  "\\usepackage{float}"
))

library(exams)
library(reticulate)
library(digest)
library(testthat)
library(knitr)

typ <- match_exams_device()
options(scipen = 999)
knitr::opts_chunk$set(
  warning = FALSE,
  message = FALSE,
  fig.showtext = FALSE,
  fig.cap = "",
  fig.keep = 'all',
  dev = c("png", "pdf"),
  dpi = 150,
  fig.pos = "H"
)

# Configuración para chunks de Python
knitr::knit_engines$set(python = function(options) {
  knitr::engine_output(options, options$code, '')
})

# Asegurar que Python esté correctamente configurado
use_python(Sys.which("python"), required = TRUE)
```

```{r DefinicionDeVariables, message=FALSE, warning=FALSE, results='asis'}
options(OutDec = ".")  # Asegurar punto decimal en este chunk

# Establecer semilla aleatoria para reproducibilidad
set.seed(sample(1:10000, 1))

# Aleatorización del contexto del problema con concordancia de género
contextos_data <- data.frame(
  nombre = c("ciudad", "localidad", "comunidad", "comarca", "provincia", "zona",
             "municipio", "distrito", "territorio", "sector", "barrio", "pueblo"),
  genero = c("f", "f", "f", "f", "f", "f",
             "m", "m", "m", "m", "m", "m"),
  articulo = c("una", "una", "una", "una", "una", "una",
               "un", "un", "un", "un", "un", "un"),
  stringsAsFactors = FALSE
)
contexto_seleccionado <- contextos_data[sample(nrow(contextos_data), 1), ]
contexto <- contexto_seleccionado$nombre
articulo_contexto <- contexto_seleccionado$articulo

# Aleatorización del tipo de competencia con concordancia de género
competencias_data <- data.frame(
  nombre = c("carrera", "competencia atlética", "olimpiada deportiva",
             "justa deportiva", "prueba atlética", "competencia deportiva",
             "maratón", "torneo deportivo", "evento deportivo",
             "campeonato", "concurso deportivo", "certamen deportivo"),
  genero = c("f", "f", "f", "f", "f", "f",
             "m", "m", "m", "m", "m", "m"),
  articulo = c("una", "una", "una", "una", "una", "una",
               "un", "un", "un", "un", "un", "un"),
  stringsAsFactors = FALSE
)
competencia_seleccionada <- competencias_data[sample(nrow(competencias_data), 1), ]
competencia <- competencia_seleccionada$nombre
articulo_competencia <- competencia_seleccionada$articulo

# Aleatorización del grupo de edad
edades <- c(
  "menores de 15 años", "menores de 16 años", "menores de 14 años",
  "niños y niñas de 10 a 15 años", "jóvenes de 12 a 15 años",
  "estudiantes de primaria y secundaria", "categoría infantil",
  "categoría juvenil", "niños y adolescentes", "estudiantes menores de edad"
)
grupo_edad <- sample(edades, 1)

# Aleatorización del premio total (en millones)
# Todos los múltiplos de 2 desde 30 hasta 400 para mayor variabilidad
premios_posibles <- seq(30, 400, by = 2)
premio_total <- sample(premios_posibles, 1)

# Aleatorización de términos para el enunciado
terminos_premiar <- c("premiar", "recompensar", "reconocer", "galardonar", "incentivar")
termino_premiar <- sample(terminos_premiar, 1)

terminos_participantes <- c("participantes", "competidores", "concursantes", "deportistas")
termino_participantes <- sample(terminos_participantes, 1)

terminos_cuenta <- c("cuenta con", "dispone de", "tiene asignados", "ha destinado", "ha reservado")
termino_cuenta <- sample(terminos_cuenta, 1)

terminos_repartiran <- c("repartirán", "distribuirán", "dividirán", "asignarán", "otorgarán")
termino_repartiran <- sample(terminos_repartiran, 1)

terminos_puestos <- c("primeros puestos", "ganadores", "mejores lugares", "primeros lugares")
termino_puestos <- sample(terminos_puestos, 1)

# Aleatorización de términos para dinero con concordancia de género
terminos_dinero_data <- data.frame(
  nombre = c("dinero", "premio", "incentivo", "monto"),
  genero = c("m", "m", "m", "m"),
  articulo_este = c("Este", "Este", "Este", "Este"),
  stringsAsFactors = FALSE
)
termino_dinero_seleccionado <- terminos_dinero_data[sample(nrow(terminos_dinero_data), 1), ]
termino_dinero <- termino_dinero_seleccionado$nombre
articulo_este_dinero <- termino_dinero_seleccionado$articulo_este

# Aleatorización de fracciones para los puestos
# NUEVA LÓGICA: Segundo puesto es fracción del dinero restante (después del primer puesto)
# Definimos conjuntos de fracciones que garanticen matemáticamente el orden correcto:
# primer puesto > segundo puesto > tercer puesto
# VERIFICADOS: todos mantienen el orden estricto decreciente con la nueva lógica
conjuntos_fracciones <- list(
  c("3/8", "4/7"),   # 37.50 > 35.71 > 26.79 ✓
  c("2/5", "3/5"),   # 40.00 > 36.00 > 24.00 ✓
  c("1/2", "2/3"),   # 50.00 > 33.33 > 16.67 ✓
  c("4/9", "3/4"),   # 44.44 > 41.67 > 13.89 ✓
  c("5/12", "2/3"),  # 41.67 > 38.89 > 19.44 ✓
  c("5/13", "3/5"),  # 38.46 > 36.92 > 24.62 ✓
  c("2/5", "4/7"),   # 40.00 > 34.29 > 25.71 ✓
  c("2/5", "5/8"),   # 40.00 > 37.50 > 22.50 ✓
  c("3/7", "3/5"),   # 42.86 > 34.29 > 22.86 ✓
  c("3/7", "2/3")    # 42.86 > 38.10 > 19.05 ✓
)

# Seleccionar un conjunto aleatorio de fracciones
indice_conjunto <- sample(1:length(conjuntos_fracciones), 1)
fracciones_seleccionadas <- conjuntos_fracciones[[indice_conjunto]]

# Asignar fracciones a los puestos
fraccion_primer_puesto <- fracciones_seleccionadas[1]
fraccion_segundo_puesto <- fracciones_seleccionadas[2]

# Convertir fracciones a valores numéricos para cálculos
convertir_fraccion <- function(fraccion) {
  partes <- strsplit(fraccion, "/")[[1]]
  return(as.numeric(partes[1]) / as.numeric(partes[2]))
}

# NUEVA LÓGICA: Segundo puesto es fracción del dinero restante
valor_primer_puesto <- convertir_fraccion(fraccion_primer_puesto)
valor_segundo_puesto_fraccion <- convertir_fraccion(fraccion_segundo_puesto)

# Calcular montos con la nueva lógica
monto_primer_puesto_exacto <- premio_total * valor_primer_puesto
dinero_restante_despues_primer_puesto <- premio_total - monto_primer_puesto_exacto
monto_segundo_puesto_exacto <- valor_segundo_puesto_fraccion * dinero_restante_despues_primer_puesto
monto_tercer_puesto_exacto <- premio_total - monto_primer_puesto_exacto - monto_segundo_puesto_exacto

# Calcular valores equivalentes para compatibilidad con el resto del código
valor_segundo_puesto <- monto_segundo_puesto_exacto / premio_total
valor_tercer_puesto <- monto_tercer_puesto_exacto / premio_total

# Verificar que el valor del tercer puesto sea positivo
test_that("El valor del tercer puesto es positivo", {
  expect_true(valor_tercer_puesto > 0)
})

# Los montos exactos ya fueron calculados con la nueva lógica arriba

# Para efectos de presentación, redondear a una decimal si es necesario
monto_primer_puesto <- round(monto_primer_puesto_exacto, 1)
monto_segundo_puesto <- round(monto_segundo_puesto_exacto, 1)
monto_tercer_puesto <- round(monto_tercer_puesto_exacto, 1)

# Ajustar el tercer puesto para asegurar que la suma sea exactamente el premio total
suma_actual <- monto_primer_puesto + monto_segundo_puesto + monto_tercer_puesto
if (abs(suma_actual - premio_total) > 0.1) {
  diferencia <- premio_total - (monto_primer_puesto + monto_segundo_puesto)
  monto_tercer_puesto <- round(diferencia, 1)
}

# Verificar que la suma de los tres montos sea igual al premio total (con tolerancia)
test_that("La suma de los tres montos es igual al premio total", {
  suma_total <- monto_primer_puesto + monto_segundo_puesto + monto_tercer_puesto
  expect_equal(suma_total, premio_total, tolerance = 0.1)
})

# Verificar que las fracciones seleccionadas garantizan el orden matemático correcto
# (esto debe ser siempre verdadero con nuestros conjuntos de fracciones corregidos)
test_that("Las fracciones mantienen el orden correcto", {
  expect_true(valor_primer_puesto > valor_segundo_puesto)
  # Usar tolerancia para comparaciones de punto flotante
  expect_true(valor_segundo_puesto >= valor_tercer_puesto - 1e-10)
})

# Test adicional para verificar que los montos calculados mantienen coherencia
test_that("Los montos calculados son coherentes con las fracciones", {
  # Verificar que los montos exactos corresponden a las fracciones (con tolerancia)
  expect_equal(monto_primer_puesto_exacto, premio_total * valor_primer_puesto, tolerance = 0.01)
  expect_equal(monto_segundo_puesto_exacto, premio_total * valor_segundo_puesto, tolerance = 0.01)
  expect_equal(monto_tercer_puesto_exacto, premio_total * valor_tercer_puesto, tolerance = 0.01)
})

# Test para asegurar coherencia: primer puesto > segundo puesto >= tercer puesto
test_that("El primer puesto recibe más que el segundo puesto", {
  expect_true(monto_primer_puesto > monto_segundo_puesto)
})

test_that("El segundo puesto recibe al menos tanto como el tercer puesto", {
  # Usar tolerancia para comparaciones de montos redondeados
  expect_true(monto_segundo_puesto >= monto_tercer_puesto - 0.1)
})

# Generar opciones de respuesta
# La respuesta correcta es el monto del segundo puesto
respuesta_correcta <- monto_segundo_puesto

# NUEVA ESTRATEGIA: Distractores completamente aleatorios sin patrones predecibles
# para que la respuesta correcta pueda aparecer en cualquier posición

# Generar un pool amplio de distractores candidatos con variación aleatoria
set.seed(NULL)  # Asegurar aleatoriedad real
distractores_candidatos <- c(
  # Variaciones aleatorias MUY amplias para cubrir todo el espectro
  round(respuesta_correcta * runif(4, 0.15, 0.6), 1),                    # 15%-60% del correcto (muy menores)
  round(respuesta_correcta * runif(4, 0.7, 0.95), 1),                    # 70%-95% del correcto (menores)
  round(respuesta_correcta * runif(4, 1.05, 1.8), 1),                    # 105%-180% del correcto (mayores)
  round(respuesta_correcta * runif(4, 2.0, 4.5), 1),                     # 200%-450% del correcto (muy mayores)

  # Errores conceptuales con rangos amplios
  round(premio_total * (valor_primer_puesto * runif(2, 0.2, 1.2)), 1),   # Variación amplia del primer puesto
  round(premio_total * (valor_segundo_puesto * runif(2, 0.3, 1.5)), 1),  # Variación amplia del segundo puesto
  round(premio_total * (valor_tercer_puesto * runif(2, 0.4, 3.0)), 1),   # Variación amplia del tercer puesto

  # Errores de cálculo con rango muy amplio
  round(respuesta_correcta + runif(3, -respuesta_correcta*0.8, respuesta_correcta*2), 1), # Errores proporcionales
  round(respuesta_correcta * runif(3, 0.3, 3.5), 1),                     # Multiplicadores muy amplios

  # Valores completamente aleatorios en rangos pedagógicamente plausibles
  round(runif(3, 5, respuesta_correcta * 0.8), 1),                       # Valores menores aleatorios
  round(runif(3, respuesta_correcta * 1.2, respuesta_correcta * 4), 1)   # Valores mayores aleatorios
)

# Filtrar distractores válidos (diferentes de la respuesta correcta y positivos)
distractores_validos <- distractores_candidatos[distractores_candidatos != respuesta_correcta & distractores_candidatos > 0]

# Asegurar que tenemos suficientes distractores únicos
distractores_validos <- unique(distractores_validos)

# Si no tenemos suficientes, generar algunos adicionales completamente aleatorios
while (length(distractores_validos) < 10) {
  nuevo_distractor <- round(respuesta_correcta * runif(1, 0.2, 3.5), 1)
  if (nuevo_distractor != respuesta_correcta && nuevo_distractor > 0) {
    distractores_validos <- c(distractores_validos, nuevo_distractor)
  }
}

# SELECCIÓN COMPLETAMENTE ALEATORIA - SIN PATRONES FORZADOS
# Simplemente seleccionar 3 distractores al azar del pool disponible
distractores_seleccionados <- sample(distractores_validos, 3)
distractor1 <- distractores_seleccionados[1]
distractor2 <- distractores_seleccionados[2]
distractor3 <- distractores_seleccionados[3]

# Verificación final de unicidad (solo si es necesario)
intentos <- 0
while (length(unique(c(respuesta_correcta, distractor1, distractor2, distractor3))) < 4 && intentos < 3) {
  # Regenerar solo los distractores que causan duplicados
  if (distractor1 == respuesta_correcta || distractor1 == distractor2 || distractor1 == distractor3) {
    distractor1 <- round(respuesta_correcta * runif(1, 0.3, 2.8), 1)
  }
  if (distractor2 == respuesta_correcta || distractor2 == distractor1 || distractor2 == distractor3) {
    distractor2 <- round(respuesta_correcta * runif(1, 0.3, 2.8), 1)
  }
  if (distractor3 == respuesta_correcta || distractor3 == distractor1 || distractor3 == distractor2) {
    distractor3 <- round(respuesta_correcta * runif(1, 0.3, 2.8), 1)
  }
  intentos <- intentos + 1
}

# Crear un vector con todas las opciones y mezclarlas
opciones <- c(respuesta_correcta, distractor1, distractor2, distractor3)
names(opciones) <- c("correcta", "distractor1", "distractor2", "distractor3")
opciones_mezcladas <- sample(opciones)

# Identificar la posición de la respuesta correcta en las opciones mezcladas
indice_correcto <- which(opciones_mezcladas == respuesta_correcta)

# Crear el vector de solución para r-exams
solucion <- rep(0, 4)
solucion[indice_correcto] <- 1

# Aleatorización de colores para la tabla TikZ
paletas_colores <- list(
  c("#4285F4", "#EA4335", "#FBBC05", "#34A853"),  # Google colors
  c("#1F77B4", "#FF7F0E", "#2CA02C", "#D62728"),  # Tableau colors
  c("#003f5c", "#58508d", "#bc5090", "#ff6361"),  # Viridis-like
  c("#0073C2", "#EFC000", "#868686", "#CD534C"),  # IBM colors
  c("#7F3C8D", "#11A579", "#3969AC", "#F2B701")   # Colorbrewer
)
paleta_seleccionada <- sample(paletas_colores, 1)[[1]]
color_encabezado <- paleta_seleccionada[1]
color_primer_puesto <- paleta_seleccionada[2]
color_segundo_puesto <- paleta_seleccionada[3]
color_tercer_puesto <- paleta_seleccionada[4]
```

```{r generar_tabla_tikz, results='asis'}
# Crear código Python para generar la tabla usando matplotlib
codigo_python <- paste0('
import matplotlib
matplotlib.use("Agg")
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.gridspec import GridSpec
import numpy as np

# Configurar colores
color_encabezado = "', color_encabezado, '"
color_primer_puesto = "', color_primer_puesto, '"
color_segundo_puesto = "', color_segundo_puesto, '"
color_tercer_puesto = "', color_tercer_puesto, '"

# Crear figura y configurar grid
fig = plt.figure(figsize=(8, 3.5))
gs = GridSpec(4, 2, height_ratios=[1, 1, 1, 1], width_ratios=[3, 7])

# Encabezado (ocupa todo el ancho)
ax_header = plt.subplot(gs[0, :])
ax_header.set_facecolor(color_encabezado)
ax_header.text(0.5, 0.5, "Distribución del premio de ', premio_total, ' millones de pesos",
              ha="center", va="center", color="white", fontweight="bold", fontsize=11)
ax_header.set_xticks([])
ax_header.set_yticks([])
ax_header.spines["top"].set_visible(True)
ax_header.spines["bottom"].set_visible(True)
ax_header.spines["left"].set_visible(True)
ax_header.spines["right"].set_visible(True)

# Primera fila
ax_p1_label = plt.subplot(gs[1, 0])
ax_p1_label.set_facecolor(color_primer_puesto)
ax_p1_label.text(0.5, 0.5, "Primer puesto", ha="center", va="center", color="white", fontweight="bold", fontsize=10)
ax_p1_label.set_xticks([])
ax_p1_label.set_yticks([])
ax_p1_label.spines["top"].set_visible(True)
ax_p1_label.spines["bottom"].set_visible(True)
ax_p1_label.spines["left"].set_visible(True)
ax_p1_label.spines["right"].set_visible(True)

ax_p1_value = plt.subplot(gs[1, 1])
ax_p1_value.set_facecolor("white")
ax_p1_value.text(0.5, 0.5, "', fraccion_primer_puesto, ' del ', termino_dinero, ' total",
                ha="center", va="center", color="black", fontsize=10)
ax_p1_value.set_xticks([])
ax_p1_value.set_yticks([])
ax_p1_value.spines["top"].set_visible(True)
ax_p1_value.spines["bottom"].set_visible(True)
ax_p1_value.spines["left"].set_visible(True)
ax_p1_value.spines["right"].set_visible(True)

# Segunda fila
ax_p2_label = plt.subplot(gs[2, 0])
ax_p2_label.set_facecolor(color_segundo_puesto)
ax_p2_label.text(0.5, 0.5, "Segundo puesto", ha="center", va="center", color="white", fontweight="bold", fontsize=10)
ax_p2_label.set_xticks([])
ax_p2_label.set_yticks([])
ax_p2_label.spines["top"].set_visible(True)
ax_p2_label.spines["bottom"].set_visible(True)
ax_p2_label.spines["left"].set_visible(True)
ax_p2_label.spines["right"].set_visible(True)

ax_p2_value = plt.subplot(gs[2, 1])
ax_p2_value.set_facecolor("white")
ax_p2_value.text(0.5, 0.5, "', fraccion_segundo_puesto, ' del ', termino_dinero, ' restante",
                ha="center", va="center", color="black", fontsize=10)
ax_p2_value.set_xticks([])
ax_p2_value.set_yticks([])
ax_p2_value.spines["top"].set_visible(True)
ax_p2_value.spines["bottom"].set_visible(True)
ax_p2_value.spines["left"].set_visible(True)
ax_p2_value.spines["right"].set_visible(True)

# Tercera fila
ax_p3_label = plt.subplot(gs[3, 0])
ax_p3_label.set_facecolor(color_tercer_puesto)
ax_p3_label.text(0.5, 0.5, "Tercer puesto", ha="center", va="center", color="white", fontweight="bold", fontsize=10)
ax_p3_label.set_xticks([])
ax_p3_label.set_yticks([])
ax_p3_label.spines["top"].set_visible(True)
ax_p3_label.spines["bottom"].set_visible(True)
ax_p3_label.spines["left"].set_visible(True)
ax_p3_label.spines["right"].set_visible(True)

ax_p3_value = plt.subplot(gs[3, 1])
ax_p3_value.set_facecolor("white")
ax_p3_value.text(0.5, 0.5, "el ', termino_dinero, ' restante",
                ha="center", va="center", color="black", fontsize=10)
ax_p3_value.set_xticks([])
ax_p3_value.set_yticks([])
ax_p3_value.spines["top"].set_visible(True)
ax_p3_value.spines["bottom"].set_visible(True)
ax_p3_value.spines["left"].set_visible(True)
ax_p3_value.spines["right"].set_visible(True)

plt.tight_layout(pad=0.5)

# Guardar la figura
plt.savefig("tabla_distribucion.png", dpi=150, bbox_inches="tight")
plt.savefig("tabla_distribucion.pdf", dpi=150, bbox_inches="tight")
plt.close()
')

# Ejecutar código Python para generar la figura
py_run_string(codigo_python)
```

Question
========

En `r articulo_contexto` `r contexto` se realizará `r articulo_competencia` `r competencia` para `r grupo_edad`. Para `r termino_premiar` a los `r termino_participantes`, `r if(contexto_seleccionado$genero == "f") "la" else "el"` `r contexto` `r termino_cuenta` `r premio_total` millones de pesos, que se `r termino_repartiran` entre los tres `r termino_puestos`, como se indica a continuación:

```{r tabla_distribucion, echo=FALSE, results='asis', fig.align='center'}
# Detectar si se está generando para Moodle u otros formatos
formatos_moodle <- c("exams2moodle", "exams2qti12", "exams2qti21", "exams2openolat")
es_moodle <- (match_exams_call() %in% formatos_moodle)

# Incluir la imagen generada por Python
if (es_moodle) {
  # Tamaño para Moodle
  cat("![](tabla_distribucion.png){width=80%}")
} else {
  # Tamaño para PDF/Word
  cat("![](tabla_distribucion.png){width=90%}")
}
```

¿Qué cantidad de `r termino_dinero` recibirá el segundo puesto?

Answerlist
----------
- `r opciones_mezcladas[1]` millones.
- `r opciones_mezcladas[2]` millones.
- `r opciones_mezcladas[3]` millones.
- `r opciones_mezcladas[4]` millones.

Solution
========

Para resolver este problema, debemos calcular qué cantidad del premio total corresponde al segundo puesto, considerando que este se calcula como una fracción del dinero restante después del primer puesto.

### Paso 1: Identificar los datos del problema
- Premio total: `r premio_total` millones de pesos
- Primer puesto: `r fraccion_primer_puesto` del `r termino_dinero` total
- Segundo puesto: `r fraccion_segundo_puesto` del `r termino_dinero` restante (después del primer puesto)
- Tercer puesto: el `r termino_dinero` restante (después del primer y segundo puesto)

### Paso 2: Calcular el monto del primer puesto
- Primer puesto: `r fraccion_primer_puesto` = `r round(valor_primer_puesto, 4)`
- Monto del primer puesto = `r round(valor_primer_puesto, 4)` × `r premio_total` = `r round(monto_primer_puesto_exacto, 2)` millones

### Paso 3: Calcular el dinero restante después del primer puesto
- Dinero restante = `r premio_total` - `r round(monto_primer_puesto_exacto, 2)` = `r round(dinero_restante_despues_primer_puesto, 2)` millones

### Paso 4: Calcular el monto del segundo puesto
El segundo puesto recibe `r fraccion_segundo_puesto` del dinero restante:

- Segundo puesto: `r fraccion_segundo_puesto` = `r round(valor_segundo_puesto_fraccion, 4)`
- Monto del segundo puesto = `r round(valor_segundo_puesto_fraccion, 4)` × `r round(dinero_restante_despues_primer_puesto, 2)` = `r round(monto_segundo_puesto_exacto, 2)` millones
- Redondeando: `r monto_segundo_puesto` millones de pesos

**Nota:** El segundo puesto se calcula como una fracción del dinero restante después del primer puesto, lo que garantiza matemáticamente que el primer puesto siempre reciba más que el segundo.

### Verificación
Comprobemos que nuestro cálculo del segundo puesto es correcto:

**Cálculo paso a paso:**

- Primer puesto: `r fraccion_primer_puesto` × `r premio_total` = `r round(monto_primer_puesto_exacto, 2)` millones
- Dinero restante: `r premio_total` - `r round(monto_primer_puesto_exacto, 2)` = `r round(dinero_restante_despues_primer_puesto, 2)` millones
- Segundo puesto: `r fraccion_segundo_puesto` × `r round(dinero_restante_despues_primer_puesto, 2)` = `r round(monto_segundo_puesto_exacto, 2)` millones

**Verificación con todos los montos:**

- Primer puesto: `r monto_primer_puesto` millones
- Segundo puesto: `r monto_segundo_puesto` millones
- Tercer puesto: `r monto_tercer_puesto` millones
- **Total: `r monto_primer_puesto + monto_segundo_puesto + monto_tercer_puesto` millones**

Como `r monto_primer_puesto + monto_segundo_puesto + monto_tercer_puesto` = `r premio_total`, confirmamos que nuestra respuesta es correcta.

**Por lo tanto, el segundo puesto recibirá `r monto_segundo_puesto` millones de pesos.**

Answerlist
----------
- `r if(solucion[1] == 1) "Verdadero" else "Falso"`
- `r if(solucion[2] == 1) "Verdadero" else "Falso"`
- `r if(solucion[3] == 1) "Verdadero" else "Falso"`
- `r if(solucion[4] == 1) "Verdadero" else "Falso"`

Meta-information
================
exname: fracciones_reparto_premio
extype: schoice
exsolution: `r paste(as.integer(solucion), collapse="")`
exshuffle: TRUE
exsection: Aritmética|Fracciones|Reparto proporcional
