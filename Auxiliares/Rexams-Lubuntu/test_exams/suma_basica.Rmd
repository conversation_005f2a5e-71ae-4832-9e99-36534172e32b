
---
output: 
  exams::exams2pdf:
    template: plain
---

# <PERSON><PERSON><PERSON><PERSON> Prueba - Matemáticas ICFES

¿Cuál es el resultado de $2 + 3$?

```{r, echo=FALSE, results="asis"}
# Generar opciones
correct <- 5
options <- c(correct, 3, 4, 6, 7)
options <- sample(options)

# Encontrar posición correcta
correct_pos <- which(options == correct)

# Mostrar opciones
for(i in 1:length(options)) {
  cat("\n", LETTERS[i], ") ", options[i], sep="")
}
```

Meta-information
================
extype: schoice
exsolution: `r paste(rep("0", 5), collapse="")`
exsolution: `r paste(replace(rep("0", 5), correct_pos, "1"), collapse="")`
exname: suma_basica

