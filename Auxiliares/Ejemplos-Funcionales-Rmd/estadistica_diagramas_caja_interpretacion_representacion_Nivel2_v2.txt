---
output:
  html_document: default
  word_document: default
  pdf_document:
    keep_tex: true
    extra_dependencies: ["graphicx", "float", "booktabs", "array"]
---
```{r setup, include=FALSE}
# Configuración para todos los formatos de salida
Sys.setlocale(category = "LC_NUMERIC", locale = "C")
options(OutDec = ".")

# Configurar el motor LaTeX globalmente
options(tikzLatex = "pdflatex")
options(tikzXelatex = FALSE)
options(tikzLatexPackages = c(
  "\\usepackage{tikz}",
  "\\usepackage{colortbl}",
  "\\usepackage{graphicx}",
  "\\usepackage{float}",
  "\\usepackage{booktabs}",
  "\\usepackage{array}"
))

library(exams)
library(reticulate)
library(digest)
library(testthat)
library(knitr)

typ <- match_exams_device()
options(scipen = 999)
knitr::opts_chunk$set(
  warning = FALSE,
  message = FALSE,
  fig.showtext = FALSE,
  fig.cap = "",
  fig.keep = 'all',
  dev = c("png", "pdf"),
  dpi = 150,
  fig.pos = "H"
)

# Configuración para chunks de Python
knitr::knit_engines$set(python = function(options) {
  knitr::engine_output(options, options$code, '')
})

# Asegurar que Python esté correctamente configurado
use_python(Sys.which("python"), required = TRUE)
```

```{r DefinicionDeVariables, message=FALSE, warning=FALSE, results='asis'}
options(OutDec = ".")  # Asegurar punto decimal en este chunk

# Establecer semilla aleatoria
set.seed(sample(1:10000, 1))

# Aleatorización del contexto del problema
contextos <- list(
  list(tipo = "estaturas", unidad = "cm", contexto = "estudiantes", articulo = "las"),
  list(tipo = "calificaciones", unidad = "puntos", contexto = "exámenes", articulo = "las"),
  list(tipo = "temperaturas", unidad = "grados C", contexto = "ciudades", articulo = "las"),
  list(tipo = "tiempos", unidad = "minutos", contexto = "atletas", articulo = "los"),
  list(tipo = "pesos", unidad = "kg", contexto = "productos", articulo = "los"),
  list(tipo = "edades", unidad = "años", contexto = "participantes", articulo = "las"),
  list(tipo = "distancias", unidad = "metros", contexto = "saltos", articulo = "las"),
  list(tipo = "velocidades", unidad = "km/h", contexto = "vehículos", articulo = "las")
)
contexto_seleccionado <- sample(contextos, 1)[[1]]

# Aleatorización de rangos de valores según el contexto
rangos_valores <- list(
  estaturas = c(150, 185),
  calificaciones = c(60, 95),
  temperaturas = c(15, 35),
  tiempos = c(45, 75),
  pesos = c(50, 85),
  edades = c(18, 45),
  distancias = c(120, 180),
  velocidades = c(80, 120)
)
rango_seleccionado <- rangos_valores[[contexto_seleccionado$tipo]]

# Aleatorización del tamaño de la muestra
tamaño_muestra <- sample(c(12, 15, 18, 20), 1)

# Aleatorización del tipo de distribución
tipos_distribucion <- c("simetrica", "asimetrica_positiva", "asimetrica_negativa")
tipo_distribucion <- sample(tipos_distribucion, 1)

# Función para generar datos con características controladas
generar_datos_controlados <- function(n, tipo, rango_min, rango_max) {
  if (tipo == "simetrica") {
    media <- (rango_min + rango_max) / 2
    desviacion <- (rango_max - rango_min) / 6
    datos <- rnorm(n, mean = media, sd = desviacion)
  } else if (tipo == "asimetrica_positiva") {
    datos <- rbeta(n, 2, 5) * (rango_max - rango_min) + rango_min
  } else if (tipo == "asimetrica_negativa") {
    datos <- rbeta(n, 5, 2) * (rango_max - rango_min) + rango_min
  }
  
  # Asegurar que los datos estén en el rango y redondear apropiadamente
  datos <- pmax(rango_min, pmin(rango_max, datos))
  if (contexto_seleccionado$tipo %in% c("calificaciones", "edades", "tiempos")) {
    return(round(datos))
  } else {
    return(round(datos, 1))
  }
}

# Generar los datos principales
datos_principales <- generar_datos_controlados(
  tamaño_muestra, 
  tipo_distribucion, 
  rango_seleccionado[1], 
  rango_seleccionado[2]
)

# Calcular estadísticas descriptivas usando método tradicional
calcular_estadisticas <- function(datos) {
  datos_ordenados <- sort(datos)
  n <- length(datos_ordenados)

  # Método tradicional para cuartiles
  # Q1: posición (n+1)/4
  # Q2 (mediana): posición (n+1)/2
  # Q3: posición 3(n+1)/4

  pos_q1 <- (n + 1) / 4
  pos_q2 <- (n + 1) / 2
  pos_q3 <- 3 * (n + 1) / 4

  # Función para interpolar cuando la posición no es entera
  interpolar <- function(datos, posicion) {
    if (posicion == floor(posicion)) {
      # Posición exacta
      return(datos[posicion])
    } else {
      # Interpolación lineal
      pos_inferior <- floor(posicion)
      pos_superior <- ceiling(posicion)
      peso <- posicion - pos_inferior

      if (pos_superior > length(datos)) {
        return(datos[pos_inferior])
      }

      return(datos[pos_inferior] * (1 - peso) + datos[pos_superior] * peso)
    }
  }

  q1 <- interpolar(datos_ordenados, pos_q1)
  mediana <- interpolar(datos_ordenados, pos_q2)
  q3 <- interpolar(datos_ordenados, pos_q3)

  list(
    minimo = unname(as.numeric(min(datos_ordenados))),
    q1 = unname(as.numeric(q1)),
    mediana = unname(as.numeric(mediana)),
    q3 = unname(as.numeric(q3)),
    maximo = unname(as.numeric(max(datos_ordenados)))
  )
}

stats_correctas <- calcular_estadisticas(datos_principales)

# Función para validar que las estadísticas sean coherentes
validar_estadisticas <- function(stats) {
  stats$minimo <= stats$q1 &&
    stats$q1 <= stats$mediana &&
    stats$mediana <= stats$q3 &&
    stats$q3 <= stats$maximo
}

# Función para comparar si dos conjuntos de estadísticas son idénticos
# (con tolerancia para errores de redondeo)
son_identicos <- function(stats1, stats2, tolerancia = 0.05) {
  abs(stats1$minimo - stats2$minimo) < tolerancia &&
    abs(stats1$q1 - stats2$q1) < tolerancia &&
    abs(stats1$mediana - stats2$mediana) < tolerancia &&
    abs(stats1$q3 - stats2$q3) < tolerancia &&
    abs(stats1$maximo - stats2$maximo) < tolerancia
}

# Función para verificar que un distractor es único
es_unico <- function(nuevo_distractor, lista_existentes, stats_correctas) {
  # Verificar que no sea idéntico a la respuesta correcta
  if (son_identicos(nuevo_distractor, stats_correctas)) {
    return(FALSE)
  }

  # Verificar que no sea idéntico a ningún distractor existente
  for (distractor_existente in lista_existentes) {
    if (son_identicos(nuevo_distractor, distractor_existente)) {
      return(FALSE)
    }
  }

  return(TRUE)
}

# Generar tres conjuntos de estadísticas para distractores únicos y válidos
generar_distractores <- function(stats_base) {
  distractores_validos <- list()

  # Calcular rango de trabajo para modificaciones
  rango_trabajo <- rango_seleccionado[2] - rango_seleccionado[1]
  incremento_base <- max(2.0, rango_trabajo * 0.08)  # Mínimo 2 unidades o 8% del rango

  # Estrategia 1: Desplazamiento hacia arriba
  distractor1 <- list(
    minimo = stats_base$minimo + incremento_base,
    q1 = stats_base$q1 + incremento_base,
    mediana = stats_base$mediana + incremento_base,
    q3 = stats_base$q3 + incremento_base,
    maximo = stats_base$maximo + incremento_base
  )

  # Verificar que esté dentro del rango y ajustar si es necesario
  if (distractor1$maximo > rango_seleccionado[2]) {
    exceso <- distractor1$maximo - rango_seleccionado[2]
    distractor1$minimo <- distractor1$minimo - exceso
    distractor1$q1 <- distractor1$q1 - exceso
    distractor1$mediana <- distractor1$mediana - exceso
    distractor1$q3 <- distractor1$q3 - exceso
    distractor1$maximo <- distractor1$maximo - exceso
  }

  # Estrategia 2: Desplazamiento hacia abajo
  distractor2 <- list(
    minimo = stats_base$minimo - incremento_base,
    q1 = stats_base$q1 - incremento_base,
    mediana = stats_base$mediana - incremento_base,
    q3 = stats_base$q3 - incremento_base,
    maximo = stats_base$maximo - incremento_base
  )

  # Verificar que esté dentro del rango y ajustar si es necesario
  if (distractor2$minimo < rango_seleccionado[1]) {
    deficit <- rango_seleccionado[1] - distractor2$minimo
    distractor2$minimo <- distractor2$minimo + deficit
    distractor2$q1 <- distractor2$q1 + deficit
    distractor2$mediana <- distractor2$mediana + deficit
    distractor2$q3 <- distractor2$q3 + deficit
    distractor2$maximo <- distractor2$maximo + deficit
  }

  # Estrategia 3: Modificar dispersión (expandir la caja)
  centro <- stats_base$mediana
  expansion_factor <- 1.3

  distractor3 <- list(
    minimo = max(rango_seleccionado[1],
                 centro - (centro - stats_base$minimo) * expansion_factor),
    q1 = max(rango_seleccionado[1] + 1,
             centro - (centro - stats_base$q1) * expansion_factor),
    mediana = stats_base$mediana + incremento_base * 0.5,  # Pequeño desplazamiento
    q3 = min(rango_seleccionado[2] - 1,
             centro + (stats_base$q3 - centro) * expansion_factor),
    maximo = min(rango_seleccionado[2],
                 centro + (stats_base$maximo - centro) * expansion_factor)
  )

  # Asegurar que la mediana esté entre Q1 y Q3
  distractor3$mediana <- max(distractor3$q1 + 0.1,
                            min(distractor3$q3 - 0.1, distractor3$mediana))

  # Agregar los distractores a la lista
  distractores_validos <- list(distractor1, distractor2, distractor3)

  # Verificar y ajustar si algún distractor no es válido
  for (i in seq_along(distractores_validos)) {
    if (!validar_estadisticas(distractores_validos[[i]])) {
      # Si no es válido, crear un distractor simple basado en desplazamiento
      factor_desplazamiento <- i * incremento_base * 0.7
      distractores_validos[[i]] <- list(
        minimo = max(rango_seleccionado[1], stats_base$minimo + factor_desplazamiento),
        q1 = stats_base$q1 + factor_desplazamiento,
        mediana = stats_base$mediana + factor_desplazamiento,
        q3 = stats_base$q3 + factor_desplazamiento,
        maximo = min(rango_seleccionado[2], stats_base$maximo + factor_desplazamiento)
      )
    }
  }

  return(distractores_validos)
}

distractores <- generar_distractores(stats_correctas)

# Crear lista de todas las opciones y mezclarlas
todas_opciones <- c(list(stats_correctas), distractores)
names(todas_opciones) <- c("correcta", "distractor1", "distractor2", "distractor3")
opciones_mezcladas <- sample(todas_opciones)

# Identificar la posición de la respuesta correcta
indice_correcto <- which(names(opciones_mezcladas) == "correcta")

# Crear el vector de solución para r-exams
solucion <- rep(0, 4)
solucion[indice_correcto] <- 1

# Validaciones matemáticas exhaustivas
test_that("Los datos están en el rango correcto", {
  expect_true(all(datos_principales >= rango_seleccionado[1]))
  expect_true(all(datos_principales <= rango_seleccionado[2]))
})

test_that("Las estadísticas correctas son coherentes", {
  expect_true(validar_estadisticas(stats_correctas))
})

test_that("Todos los distractores son válidos", {
  for (i in 1:length(distractores)) {
    expect_true(validar_estadisticas(distractores[[i]]),
                info = paste("Distractor", i, "no es válido"))
  }
})

# Tests básicos de validación (simplificados para mayor robustez)
test_that("Configuración básica es válida", {
  expect_true(length(distractores) == 3)
  expect_true(validar_estadisticas(stats_correctas))
})

# Aleatorización de términos para el enunciado
terminos_tabla <- c("cuadro", "registro", "listado")
termino_tabla <- sample(terminos_tabla, 1)

terminos_diagrama <- c("diagramas de caja", "gráficos de caja y bigotes", "boxplots", "diagramas de cajas")
termino_diagrama <- sample(terminos_diagrama, 1)

terminos_corresponde <- c("corresponde", "representa", "refleja", "muestra")
termino_corresponde <- sample(terminos_corresponde, 1)
```

```{r generar_tabla_datos, message=FALSE, warning=FALSE, results='asis'}
options(OutDec = ".")  # Asegurar punto decimal en este chunk

# Organizar los datos en dos columnas para la tabla (SIN ORDENAR para mayor desafío)
datos_desordenados <- sample(datos_principales)  # Mezclar aleatoriamente los datos
n_filas <- ceiling(length(datos_desordenados) / 2)

# Crear matriz para la tabla
if (length(datos_desordenados) %% 2 == 0) {
  # Número par de datos
  col1 <- datos_desordenados[1:n_filas]
  col2 <- datos_desordenados[(n_filas + 1):length(datos_desordenados)]
} else {
  # Número impar de datos
  col1 <- datos_desordenados[1:n_filas]
  col2 <- c(datos_desordenados[(n_filas + 1):length(datos_desordenados)], "")
}

# Crear la tabla usando TikZ para mejor control visual
tabla_tikz <- c(
  "\\begin{tikzpicture}",
  "\\node[inner sep=0pt] {",
  "  \\begin{tabular}{|c|c|}",
  "    \\hline",
  paste0("    \\textbf{", contexto_seleccionado$tipo, " (", contexto_seleccionado$unidad, ")} & \\textbf{", contexto_seleccionado$tipo, " (", contexto_seleccionado$unidad, ")} \\\\"),
  "    \\hline"
)

for (i in seq_along(col1)) {
  if (col2[i] != "") {
    tabla_tikz <- c(tabla_tikz, paste0("    ", col1[i], " & ", col2[i], " \\\\"))
  } else {
    tabla_tikz <- c(tabla_tikz, paste0("    ", col1[i], " & \\\\"))
  }
  tabla_tikz <- c(tabla_tikz, "    \\hline")
}

tabla_tikz <- c(tabla_tikz,
  "  \\end{tabular}",
  "};",
  "\\end{tikzpicture}"
)
```

```{r generar_diagramas_caja, message=FALSE, warning=FALSE}
options(OutDec = ".")  # Asegurar punto decimal en este chunk

# Generar cada diagrama por separado usando método directo sin datos sintéticos
letras <- c("A", "B", "C", "D")
colores <- c("#2E86AB", "#A23B72", "#F18F01", "#C73E1D")

# Crear un mapeo para saber qué letra corresponde a qué estadística
mapeo_letras_stats <- list()

for (i in 1:4) {
  stats <- opciones_mezcladas[[i]]
  letra <- letras[i]
  color <- colores[i]

  # Guardar el mapeo entre letra y estadísticas
  mapeo_letras_stats[[letra]] <- names(opciones_mezcladas)[i]

  # Validar que todos los valores son escalares únicos
  stopifnot(
    length(stats$q1) == 1,
    length(stats$mediana) == 1,
    length(stats$q3) == 1,
    length(stats$minimo) == 1,
    length(stats$maximo) == 1
  )

  # Formatear números con control preciso
  old_locale <- Sys.getlocale("LC_NUMERIC")
  Sys.setlocale("LC_NUMERIC", "C")

  q1_fmt <- sprintf("%.3f", stats$q1)
  mediana_fmt <- sprintf("%.3f", stats$mediana)
  q3_fmt <- sprintf("%.3f", stats$q3)
  minimo_fmt <- sprintf("%.3f", stats$minimo)
  maximo_fmt <- sprintf("%.3f", stats$maximo)

  # Restaurar locale original
  Sys.setlocale("LC_NUMERIC", old_locale)
  letra_lower <- tolower(letra)

  # Código Python que dibuja directamente el boxplot con valores exactos
  codigo_diagrama <- paste0("
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np

plt.style.use('default')
plt.rcParams['font.size'] = 12
plt.rcParams['axes.linewidth'] = 1.2

# Valores exactos del diagrama de caja
min_val = ", minimo_fmt, "
q1 = ", q1_fmt, "
median = ", mediana_fmt, "
q3 = ", q3_fmt, "
max_val = ", maximo_fmt, "

y_min = ", rango_seleccionado[1] - 5, "
y_max = ", rango_seleccionado[2] + 5, "

fig, ax = plt.subplots(1, 1, figsize=(4, 6))

# Dibujar el diagrama de caja manualmente con valores exactos
box_width = 0.6
x_center = 1

# Dibujar bigotes (whiskers)
ax.plot([x_center, x_center], [min_val, q1], 'k-', linewidth=1.5)
ax.plot([x_center, x_center], [q3, max_val], 'k-', linewidth=1.5)

# Dibujar tapas de los bigotes
ax.plot([x_center - box_width/4, x_center + box_width/4],
        [min_val, min_val], 'k-', linewidth=1.5)
ax.plot([x_center - box_width/4, x_center + box_width/4],
        [max_val, max_val], 'k-', linewidth=1.5)

# Dibujar la caja (rectángulo)
box = patches.Rectangle((x_center - box_width/2, q1),
                       box_width, q3 - q1,
                       linewidth=1.5,
                       edgecolor='black',
                       facecolor='", color, "',
                       alpha=0.7)
ax.add_patch(box)

# Dibujar la mediana
ax.plot([x_center - box_width/2, x_center + box_width/2],
        [median, median], 'white', linewidth=2)

# Configurar ejes y etiquetas
ax.set_xlim(0.4, 1.6)
ax.set_ylim(y_min, y_max)
ax.set_ylabel('", contexto_seleccionado$tipo, " (", contexto_seleccionado$unidad, ")',
              fontsize=12)
ax.grid(True, alpha=0.6, linestyle='-', linewidth=0.8, color='gray')
ax.set_xticks([])

plt.tight_layout()

plt.savefig('diagrama_", letra_lower, ".png', dpi=150, bbox_inches='tight',
           facecolor='white', edgecolor='none')
plt.savefig('diagrama_", letra_lower, ".pdf', dpi=150, bbox_inches='tight',
           facecolor='white', edgecolor='none')
plt.close()
")

  py_run_string(codigo_diagrama)
}

# Identificar qué letra corresponde a la respuesta correcta
letra_correcta <- names(mapeo_letras_stats)[which(mapeo_letras_stats == "correcta")]
```

Question
========

El siguiente `r termino_tabla` muestra `r contexto_seleccionado$articulo` `r contexto_seleccionado$tipo` de un grupo de `r contexto_seleccionado$contexto`:

```{r mostrar_tabla, echo=FALSE, results='asis'}
# Detectar formato de salida
formatos_moodle <- c("exams2moodle", "exams2qti12", "exams2qti21", "exams2openolat")
es_moodle <- (match_exams_call() %in% formatos_moodle)

if (es_moodle) {
  # Para Moodle, usar tabla HTML simple
  cat("<table border='1' style='border-collapse: collapse; margin: 0 auto;'>")
  cat("<tr><th style='padding: 8px; text-align: center; background-color: #f0f0f0;'>")
  cat(paste0(contexto_seleccionado$tipo, " (", contexto_seleccionado$unidad, ")"))
  cat("</th><th style='padding: 8px; text-align: center; background-color: #f0f0f0;'>")
  cat(paste0(contexto_seleccionado$tipo, " (", contexto_seleccionado$unidad, ")"))
  cat("</th></tr>")

  for (i in seq_along(col1)) {
    cat("<tr>")
    cat("<td style='padding: 8px; text-align: center;'>", col1[i], "</td>")
    if (col2[i] != "") {
      cat("<td style='padding: 8px; text-align: center;'>", col2[i], "</td>")
    } else {
      cat("<td style='padding: 8px; text-align: center;'></td>")
    }
    cat("</tr>")
  }
  cat("</table>")
} else {
  # Para PDF/Word, usar TikZ
  include_tikz(tabla_tikz,
               name = "tabla_datos",
               markup = "markdown",
               format = typ,
               packages = c("tikz", "colortbl"),
               width = "8cm")
}
```

¿Cuál de los siguientes `r termino_diagrama` `r termino_corresponde` mejor los datos del `r termino_tabla`?

Answerlist
----------

```{r mostrar_opciones, echo=FALSE, results='asis'}
# Mostrar las opciones con sus respectivas imágenes siguiendo el patrón de ejemplos funcionales
cat("-\n")
if (es_moodle) {
  cat("![](diagrama_a.png){width=60%}\n\n")
} else {
  cat("![](diagrama_a.png){width=70%}\n\n")
}

cat("-\n")
if (es_moodle) {
  cat("![](diagrama_b.png){width=60%}\n\n")
} else {
  cat("![](diagrama_b.png){width=70%}\n\n")
}

cat("-\n")
if (es_moodle) {
  cat("![](diagrama_c.png){width=60%}\n\n")
} else {
  cat("![](diagrama_c.png){width=70%}\n\n")
}

cat("-\n")
if (es_moodle) {
  cat("![](diagrama_d.png){width=60%}\n\n")
} else {
  cat("![](diagrama_d.png){width=70%}\n\n")
}
```

Solution
========

Para resolver este problema, necesitamos analizar los datos de la tabla y compararlos con las características de cada diagrama de caja y bigotes. Seguiremos un proceso sistemático y detallado:

### Paso 1: Organizar y analizar los datos de la tabla

Para resolver este problema, primero debemos organizar los datos de la tabla de menor a mayor y luego calcular las medidas estadísticas necesarias.

**Datos de la tabla (sin ordenar):**
`r paste(datos_principales, collapse = ", ")`

**Datos ordenados de menor a mayor:**
`r paste(sort(datos_principales), collapse = ", ")`

**Total de datos:** n = `r length(datos_principales)`

### Paso 2: Calcular paso a paso las medidas estadísticas clave

Para construir un diagrama de caja y bigotes necesitamos cinco valores estadísticos. Vamos a calcular cada uno detalladamente:

#### 2.1 Valor mínimo
El valor mínimo es simplemente el menor valor del conjunto de datos ordenados.
**Valor mínimo:** `r stats_correctas$minimo` `r contexto_seleccionado$unidad`

#### 2.2 Primer cuartil (Q$_1$)
El primer cuartil es el valor que deja el 25% de los datos por debajo de él.

**Posición de Q$_1$:** (n + 1) ÷ 4 = (`r length(datos_principales)` + 1) ÷ 4 = `r (length(datos_principales) + 1) / 4`

```{r calcular_q1_detallado, echo=FALSE, results='asis'}
n <- length(datos_principales)
pos_q1 <- (n + 1) / 4
datos_ord <- sort(datos_principales)

if (pos_q1 == floor(pos_q1)) {
  # Posición exacta
  cat("Como la posición es un número entero (", pos_q1, "), Q$_1$ es el valor en la posición ", pos_q1, ":\n")
  cat("**Q$_1$ =** ", datos_ord[pos_q1], " ", contexto_seleccionado$unidad, "\n")
} else {
  # Método tradicional: redondear hacia arriba
  pos_redondeada <- ceiling(pos_q1)
  cat("Como la posición no es un número entero (", pos_q1, "), usando el método tradicional tomamos el valor en la posición redondeada hacia arriba (", pos_redondeada, "):\n")
  cat("**Q$_1$ =** ", datos_ord[pos_redondeada], " ", contexto_seleccionado$unidad, "\n")
}
```

#### 2.3 Mediana (Q$_2$)
La mediana es el valor central que divide los datos en dos mitades iguales.

**Posición de la mediana:** (n + 1) ÷ 2 = (`r length(datos_principales)` + 1) ÷ 2 = `r (length(datos_principales) + 1) / 2`

```{r calcular_mediana_detallado, echo=FALSE, results='asis'}
pos_mediana <- (n + 1) / 2

if (pos_mediana == floor(pos_mediana)) {
  # Posición exacta
  cat("Como la posición es un número entero (", pos_mediana, "), la mediana es el valor en la posición ", pos_mediana, ":\n")
  cat("**Mediana =** ", datos_ord[pos_mediana], " ", contexto_seleccionado$unidad, "\n")
} else {
  # Método tradicional: promedio de los dos valores centrales
  pos_inf <- floor(pos_mediana)
  pos_sup <- ceiling(pos_mediana)

  cat("Como la posición no es un número entero (", pos_mediana, "), usando el método tradicional calculamos el promedio de los valores en las posiciones ", pos_inf, " y ", pos_sup, ":\n")
  cat("- Valor en posición ", pos_inf, ": ", datos_ord[pos_inf], " ", contexto_seleccionado$unidad, "\n")
  cat("- Valor en posición ", pos_sup, ": ", datos_ord[pos_sup], " ", contexto_seleccionado$unidad, "\n")
  cat("**Mediana = (", datos_ord[pos_inf], " + ", datos_ord[pos_sup], ") ÷ 2 = ", round(stats_correctas$mediana, 1), "** ", contexto_seleccionado$unidad, "\n")
}
```

#### 2.4 Tercer cuartil (Q$_3$)
El tercer cuartil es el valor que deja el 75% de los datos por debajo de él.

**Posición de Q$_3$:** 3 × (n + 1) ÷ 4 = 3 × (`r length(datos_principales)` + 1) ÷ 4 = `r 3 * (length(datos_principales) + 1) / 4`

```{r calcular_q3_detallado, echo=FALSE, results='asis'}
pos_q3 <- 3 * (n + 1) / 4

if (pos_q3 == floor(pos_q3)) {
  # Posición exacta
  cat("Como la posición es un número entero (", pos_q3, "), Q$_3$ es el valor en la posición ", pos_q3, ":\n")
  cat("**Q$_3$ =** ", datos_ord[pos_q3], " ", contexto_seleccionado$unidad, "\n")
} else {
  # Método tradicional: redondear hacia arriba
  pos_redondeada <- ceiling(pos_q3)
  cat("Como la posición no es un número entero (", pos_q3, "), usando el método tradicional tomamos el valor en la posición redondeada hacia arriba (", pos_redondeada, "):\n")
  cat("**Q$_3$ =** ", datos_ord[pos_redondeada], " ", contexto_seleccionado$unidad, "\n")
}
```

#### 2.5 Valor máximo
El valor máximo es simplemente el mayor valor del conjunto de datos ordenados.
**Valor máximo:** `r stats_correctas$maximo` `r contexto_seleccionado$unidad`

### Paso 3: Resumen de las medidas estadísticas calculadas

**Resumen de los cinco valores estadísticos:**

- **Valor mínimo:** `r stats_correctas$minimo` `r contexto_seleccionado$unidad`
- **Primer cuartil (Q$_1$):** `r stats_correctas$q1` `r contexto_seleccionado$unidad`
- **Mediana (Q$_2$):** `r stats_correctas$mediana` `r contexto_seleccionado$unidad`
- **Tercer cuartil (Q$_3$):** `r stats_correctas$q3` `r contexto_seleccionado$unidad`
- **Valor máximo:** `r stats_correctas$maximo` `r contexto_seleccionado$unidad`

### Paso 4: Interpretar el diagrama de caja y bigotes

En un diagrama de caja y bigotes, cada elemento tiene un significado específico:

- **Bigote inferior:** Se extiende desde el valor mínimo (`r stats_correctas$minimo`) hasta Q$_1$ (`r stats_correctas$q1`)
- **Borde inferior de la caja:** Representa Q$_1$ (`r stats_correctas$q1`)
- **Línea central dentro de la caja:** Representa la mediana (`r stats_correctas$mediana`)
- **Borde superior de la caja:** Representa Q$_3$ (`r stats_correctas$q3`)
- **Bigote superior:** Se extiende desde Q$_3$ (`r stats_correctas$q3`) hasta el valor máximo (`r stats_correctas$maximo`)
- **Altura de la caja:** Representa la amplitud intercuartílica (Q$_3$ - Q$_1$ = `r stats_correctas$q3` - `r stats_correctas$q1` = `r round(stats_correctas$q3 - stats_correctas$q1, 1)`)

### Paso 5: Analizar cada opción

**Características que debe tener el diagrama correcto:**

- Bigote inferior terminando en `r stats_correctas$minimo` `r contexto_seleccionado$unidad`
- Borde inferior de la caja en `r stats_correctas$q1` `r contexto_seleccionado$unidad`
- Línea central (mediana) en `r stats_correctas$mediana` `r contexto_seleccionado$unidad`
- Borde superior de la caja en `r stats_correctas$q3` `r contexto_seleccionado$unidad`
- Bigote superior terminando en `r stats_correctas$maximo` `r contexto_seleccionado$unidad`

### Conclusión

El Diagrama que representa correctamente los datos de la tabla, es

```{r mostrar_diagrama_correcto, echo=FALSE, results='asis', fig.align='center'}
# Mostrar el diagrama correcto en la solución usando la letra correcta identificada
letra_correcta_lower <- tolower(letra_correcta)
nombre_archivo <- paste0("diagrama_", letra_correcta_lower, ".png")
if (es_moodle) {
  cat("![](", nombre_archivo, "){width=60%}")
} else {
  cat("![](", nombre_archivo, "){width=70%}")
}
```

ya que sus medidas estadísticas (mínimo, Q$_1$, mediana, Q$_3$ y máximo) coinciden exactamente con los valores calculados a partir de los datos originales.



Answerlist
----------
- `r if(solucion[1] == 1) "Verdadero" else "Falso"`
- `r if(solucion[2] == 1) "Verdadero" else "Falso"`
- `r if(solucion[3] == 1) "Verdadero" else "Falso"`
- `r if(solucion[4] == 1) "Verdadero" else "Falso"`

Meta-information
================
exname: estadistica_diagramas_caja_interpretacion
extype: schoice
exsolution: `r paste(as.integer(solucion), collapse="")`
exshuffle: TRUE
exsection: Estadística|Medidas de posición|Diagramas de caja|Interpretación de gráficos
exextra[Type]: Interpretación y representación
exextra[Level]: 2
exextra[Language]: es
exextra[Course]: Matemáticas ICFES
