\documentclass[10pt,a4paper]{article}

%% paquetes
\usepackage[utf8]{inputenc}
\usepackage{a4wide,color,verbatim,Sweave,url,xargs,amsmath,booktabs,longtable,eurosym}
\usepackage{tikz}
\usetikzlibrary{arrows,shapes.gates.logic.US,calc}

%% nuevos entornos
\newenvironment{question}{\item}{}
\newenvironment{solution}{\comment}{\endcomment}
\newenvironment{answerlist}{\renewcommand{\labelenumii}{(\alph{enumii})}\begin{enumerate}}{\end{enumerate}}

%% párrafos
\setlength{\parskip}{0.7ex plus0.1ex minus0.1ex}
\setlength{\parindent}{0em}

\begin{document}

\begin{enumerate}

<<echo=FALSE, results=hide>>=
library(exams)
# usar "tex" para exams2nops(), "svg" para exams2html(), y el mismo
# tipo de gráfico (pdf, svg, png) que la llamada actual de xweave()
typ <- switch(match_exams_call(),
  "exams2nops" = "tex",
  "exams2html" = "svg",
  match_exams_device()
)

# operadores lógicos
ops <- list(
  "or"   = function(a, b) a | b,
  "and"  = function(a, b) a & b,
  "xor"  = function(a, b) !(a & b) & (a | b),
  "nand" = function(a, b) !(a & b),
  "nor"  = function(a, b) !(a | b)
)

# puerta tikz
tikz_gate <- function(op) {
  c("\\begin{tikzpicture}[thick]",
    paste0("  \\node[left,draw, logic gate inputs=nn, ", op," gate US,fill=none,,scale=2.5] (G1) at (0,0) {};"),
    "  \\draw (G1.output) --++ (0.5,0) node[right] (y) {$y$};",
    "  \\draw (G1.input 1) --++ (-0.5,0) node[left] {$a$};",
    "  \\draw (G1.input 2) --++ (-0.5,0) node[left] {$b$};",
    "\\end{tikzpicture}")
}

tikz_gate_libraries <- c("arrows", "shapes.gates.logic.US", "calc")

# tabla de verdad tikz
tikz_truth_table <- function(op) {
  a <- c(0, 0, 1, 1)
  b <- c(0, 1, 0, 1)
  if(is.character(op)) op <- ops[[op]]

  c("\\node {",
    "  \\begin{tabular}{ccc}\\toprule",
    "    $a$ & $b$ & $y$\\\\",
    "    \\midrule",
    paste("   ", a, "&", b, "&", as.numeric(op(a, b)), "\\\\"),
    "    \\bottomrule",
    "  \\end{tabular}",
    "};")
}

## muestra de operadores
ops4 <- sample(names(ops), 4)
ops3 <- sample(ops4, 3)
ops1 <- sample(ops4, 1)
sol <- ops3 == ops1
sol <- c(sol, !any(sol))
ans <- c("A", "B", "C", "Ninguna de estas")
@

\begin{question}

Considere las siguientes puertas que codifican operadores lógicos:

\begin{tabular}{ccc}
A & B & C\\

<<echo=FALSE, results=tex>>=
include_tikz(tikz_gate(ops3[1]), name = "A",
  format = typ, width = "2.5cm", library = tikz_gate_libraries)
@

&

<<echo=FALSE, results=tex>>=
include_tikz(tikz_gate(ops3[2]), name = "B",
  format = typ, width = "2.5cm", library = tikz_gate_libraries)
@

&

<<echo=FALSE, results=tex>>=
include_tikz(tikz_gate(ops3[3]), name = "C",
  format = typ, width = "2.5cm", library = tikz_gate_libraries)
@

\end{tabular}

¿Cuál de estas puertas corresponde a la siguiente tabla de verdad lógica?

<<echo=FALSE, results=tex>>=
include_tikz(tikz_truth_table(ops1), name = "table",
  format = typ, width = "1.5cm", packages = "booktabs")
@


<<echo=FALSE, results=tex>>=
answerlist(ans)
@

\end{question}


\begin{solution}

La tabla de verdad codifica el operador lógico \textbf{\Sexpr{ops1}}.

Las puertas mostradas codifican los siguientes operadores lógicos:
A = \textbf{\Sexpr{ops3[1]}},
B = \textbf{\Sexpr{ops3[2]}},
C = \textbf{\Sexpr{ops3[3]}}.

Por lo tanto, la tabla de verdad corresponde a:
\Sexpr{ans[sol]}.

<<echo=FALSE, results=tex>>=
answerlist(ifelse(sol, "Verdadero", "Falso"))
@

\end{solution}

\exname{Operadores lógicos}
\extype{schoice}
\exsolution{\Sexpr{mchoice2string(sol)}}

\end{enumerate}
\end{document}
