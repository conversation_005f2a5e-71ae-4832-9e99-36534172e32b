\documentclass[10pt,a4paper]{article}

%% paquetes
\usepackage[utf8]{inputenc}
\usepackage{a4wide,color,verbatim,Sweave,url,xargs,amsmath,booktabs,longtable,eurosym}
\usepackage{tikz}
\usetikzlibrary{automata,positioning}

%% nuevos entornos
\newenvironment{question}{\item}{}
\newenvironment{solution}{\comment}{\endcomment}
\newenvironment{answerlist}{\renewcommand{\labelenumii}{(\alph{enumii})}\begin{enumerate}}{\end{enumerate}}

%% comandos para exams
\newcommand{\exname}[1]{\def\@exname{#1}}
\newcommand{\extype}[1]{\def\@extype{#1}}
\newcommand{\exsolution}[1]{\def\@exsolution{#1}}
\newcommand{\extol}[1]{\def\@extol{#1}}
\newcommand{\exclozetype}[1]{\def\@exclozetype{#1}}
\newcommand{\exshuffle}[1]{\def\@exshuffle{#1}}

%% párrafos
\setlength{\parskip}{0.7ex plus0.1ex minus0.1ex}
\setlength{\parindent}{0em}

\begin{document}
\SweaveOpts{concordance=TRUE}

\begin{enumerate}

<<echo=FALSE, results=hide>>=
library(exams)
# usar el mismo tipo de gráfico (pdf, svg, png) que la llamada actual de xweave()
# excepto para exams2nops() donde se usa tex
typ <- match_exams_device()
if(match_exams_call() == "exams2nops") typ <- "tex"


## código tikz del autómata con dos marcadores de posición
automaton <- '
\\begin{tikzpicture}[shorten >=1pt,node distance=2cm]
\\node[state, initial%s] (A)              {A};
\\node[state%s]          (B) [below=of A] {B};
\\node[state%s]          (C) [right=of A] {C};
\\node[state%s]          (D) [below=of C] {D};
\\path[->] (A) edge [bend left=20] node [right] {1} (B)
           (A) edge [bend left=20] node [above] {0} (C)
           (B) edge [bend left=20] node [left]  {1} (A)
           (B) edge [bend left=20] node [above] {0} (D)
           (C) edge [bend left=20] node [right] {1} (D)
           (C) edge [bend left=20] node [below] {0} (A)
           (D) edge [bend left=20] node [left]  {1} (C)
           (D) edge [bend left=20] node [below] {0} (B);
\\end{tikzpicture}
'

## muestra el estado de aceptación e inserciones
accept <- sample(c("A", "B", "C", "D"), 1)
automaton <- sprintf(automaton,
  if(accept == "A") ", accepting" else "",
  if(accept == "B") ", accepting" else "",
  if(accept == "C") ", accepting" else "",
  if(accept == "D") ", accepting" else "")

## asegurar secuencias únicas
ok <- FALSE
while(!ok) {

## cinco secuencias aleatorias
sequences <- replicate(5,
  sample(0:1, sample(4:7, 1), replace = TRUE),
  simplify = FALSE
)

## comprobar si secuencias pares o impares conducen al estado de aceptación
even0 <- accept %in% c("A", "B")
even1 <- accept %in% c("A", "C")
n1 <- sapply(sequences, sum)
n0 <- sapply(sequences, length) - n1
sol <- (even0 == (n0 %% 2L < 1L)) & (even1 == (n1 %% 2L < 1L))
sequences <- sapply(sequences, paste, collapse = "")

ok <- all(!duplicated(sequences)) && any(sol) && any(!sol)
}
@


\begin{question}

Considere el siguiente autómata con estado inicial A y estado de aceptación \Sexpr{accept}:

<<echo=FALSE, results=tex>>=
include_tikz(automaton, name = "automaton", format = typ,
  library = c("automata", "positioning"),
  width = "5cm")
@


¿Cuáles de las siguientes secuencias son aceptadas?

<<echo=FALSE, results=tex>>=
answerlist(sequences)
@

\end{question}


\begin{solution}

El autómata dado acepta cadenas de entrada que consisten en un número
\Sexpr{if(even1) "par" else "impar"} de unos y un número
\Sexpr{if(even0) "par" else "impar"} de ceros.

<<echo=FALSE, results=tex>>=
answerlist(ifelse(sol, "Aceptada", "No aceptada"))
@

\end{solution}


\exname{Autómata}
\extype{mchoice}
\exsolution{\Sexpr{mchoice2string(sol)}}

\end{enumerate}
\end{document}
