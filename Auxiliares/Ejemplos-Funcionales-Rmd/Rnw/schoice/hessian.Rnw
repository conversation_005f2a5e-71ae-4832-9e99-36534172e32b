<<echo=FALSE, results=hide>>=
coef <- sample(c(2:9, -(2:9)), 3, replace = TRUE)
x <- sample(c(-5:5), 2, replace = TRUE)
H <- matrix(c(2 * coef[1], coef[2], coef[2], 2 * coef[3]),
  nrow = 2, ncol = 2)

ix <- sample(1:4, 1, prob=c(0.35, 0.15, 0.15, 0.35))
ixt <- c("upper left", "upper right", "lower left", "lower right")[ix]
ixn <- c("11", "12", "21", "22")[ix]
sol <- H[ix]

err <- unique(H[-ix])
err <- err[err != sol]
sc <- num_to_schoice(sol, wrong = err, range = -25:25, method = "delta", delta = 1, digits = 0)

plus <- ifelse(coef < 0, "", "+")
@

\begin{question}
Compute the Hessian of the function
\begin{eqnarray*}
  f(x_1, x_2) = \Sexpr{coef[1]} x_1^{2} \Sexpr{plus[2]} \Sexpr{coef[2]}  x_1  x_2 \Sexpr{plus[3]} \Sexpr{coef[3]}  x_2^{2}
\end{eqnarray*}
at $(x_1, x_2) = (\Sexpr{x[1]}, \Sexpr{x[2]})$.
What is the value of the \Sexpr{ixt} element?

<<echo=FALSE, results=tex>>=
answerlist(sc$questions)
@
\end{question}

\begin{solution}
The first-order partial derivatives are 
\begin{eqnarray*}
  f'_1(x_1, x_2) &=& \Sexpr{H[1,1]} x_1 \Sexpr{plus[2]} \Sexpr{H[1,2]} x_2  \\
  f'_2(x_1, x_2) &=& \Sexpr{H[2,1]} x_1 \Sexpr{plus[3]} \Sexpr{H[2,2]} x_2
\end{eqnarray*}
and the second-order partial derivatives are
\begin{eqnarray*}
  f''_{11}(x_1, x_2) &=& \Sexpr{H[1,1]}\\
  f''_{12}(x_1, x_2) &=& \Sexpr{H[1,2]}\\
  f''_{21}(x_1, x_2) &=& \Sexpr{H[2,1]}\\
  f''_{22}(x_1, x_2) &=& \Sexpr{H[2,2]}
\end{eqnarray*}

Therefore the Hessian is
\begin{eqnarray*}
  f''(x_1, x_2) = \Sexpr{toLatex(H)}
\end{eqnarray*}
independent of $x_1$ and $x_2$. Thus, the \Sexpr{ixt} element is:
$f''_{\Sexpr{ixn}}(\Sexpr{x[1]}, \Sexpr{x[2]}) = \Sexpr{sol}$.


<<echo=FALSE, results=tex>>=
answerlist(ifelse(sc$solutions, "True", "False"))
@

\end{solution}

%% \extype{schoice}
%% \exsolution{\Sexpr{mchoice2string(sc$solutions)}}
%% \exname{Hessian}
