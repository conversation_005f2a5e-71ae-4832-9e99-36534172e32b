
\begin{question}

Consider the following gates coding logical operators:

\begin{tabular}{ccc}
A & B & C\\

\includegraphics[width=2.5cm]{A.png}
&

\includegraphics[width=2.5cm]{B.png}
&

\includegraphics[width=2.5cm]{C.png}
\end{tabular}

Which of these gates belongs to the following logical truth table?

\includegraphics[width=1.5cm]{table.png}

\begin{answerlist}
  \item A
  \item B
  \item C
  \item None of these
\end{answerlist}
\end{question}


\begin{solution}

The truth table codes the logical \textbf{or} operator.

The gates displayed code the following logical operators:
A = \textbf{nand},
B = \textbf{nor},
C = \textbf{xor}.

Hence, the truth table corresponds to:
None of these.

\begin{answerlist}
  \item False
  \item False
  \item False
  \item True
\end{answerlist}
\end{solution}

\exname{Logical operators}
\extype{schoice}
\exsolution{0001}
