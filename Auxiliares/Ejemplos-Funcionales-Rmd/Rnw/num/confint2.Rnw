<<echo=FALSE, results=hide>>=
## DATA GENERATION
n <- sample(50:150, 1)
y <- rnorm(n, runif(1, 100, 200), runif(1, 10, 15))

## QUESTION/ANSWER GENERATION
Mean <- round(mean(y), digits = 1)
Var <- round(var(y), digits = 1)
sd <- sqrt(Var/n)
LB <- round(Mean - 1.96*sd, 3)
UB <- round(Mean + 1.96*sd, 3)
@

\begin{question}
The daily expenses of summer tourists in Vienna are analyzed. A
survey with $\Sexpr{n}$ tourists is conducted. This shows that the
tourists spend on average $\Sexpr{Mean}$ EUR. The sample variance
$s^2_{n-1}$ is equal to $\Sexpr{Var}$.

Determine a $95\%$ confidence interval for the average daily
expenses (in EUR) of a tourist.

\begin{answerlist}
\item What is the lower confidence bound?
\item What is the upper confidence bound?
\end{answerlist}
\end{question}

%% SOLUTION
\begin{solution}
The $95\%$ confidence interval for the average expenses $\mu$ is
given by:
\begin{eqnarray*}
&   & \left[\bar{y} \, - \, 1.96\sqrt{\frac{s_{n-1}^2}{n}}, \; 
  \bar{y} \, + \, 1.96\sqrt{\frac{s_{n-1}^2}{n}}\right] \\
& = & \left[ \Sexpr{Mean} \, - \, 1.96\sqrt{\frac{\Sexpr{Var}}{\Sexpr{n}}}, \;
             \Sexpr{Mean} \, + \, 1.96\sqrt{\frac{\Sexpr{Var}}{\Sexpr{n}}}\right] \\
& = & \left[\Sexpr{LB}, \, \Sexpr{UB}\right].
\end{eqnarray*}

\begin{answerlist}
\item The lower confidence bound is $\Sexpr{LB}$.
\item The upper confidence bound is $\Sexpr{UB}$.
\end{answerlist}
\end{solution}

%% META-INFORMATION
%% \extype{cloze}
%% \exclozetype{num|num}
%% \exsolution{\Sexpr{LB}|\Sexpr{UB}}
%% \exname{Confidence interval}
%% \extol{0.01}
