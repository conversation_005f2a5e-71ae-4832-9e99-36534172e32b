\documentclass[a4paper]{article}

\usepackage[utf8]{inputenc}
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{enumitem}

% Definir los entornos necesarios para exams
\newenvironment{question}{}{}
\newenvironment{solution}{}{}
\newenvironment{answerlist}{\begin{enumerate}}{\end{enumerate}}

\begin{document}
\SweaveOpts{concordance=TRUE}

<<echo=FALSE, results=hide>>=
library(exams)
n <- sample(8:15, 1)
y <- rnorm(n, runif(1, 4940, 4990), runif(1, 30, 50))
alpha <- sample(c(0.1, 0.05, 0.01), 1)

Mean <- round(mean(y), digits = 1)
Var <- round(var(y), digits = 1)
sd <- sqrt(Var/n)
fact <- round(qt(1 - alpha/2, df = n - 1), digits = 4)
facn <- round(qnorm(1 - alpha/2), digits = 4)
LBt <- round(Mean - fact * sd, digits = 3)
UBt <- round(Mean + fact * sd, digits = 3)
LBn <- round(Mean - facn * sd, digits = 3)
UBn <- round(Mean + facn * sd, digits = 3)

## use extended Moodle processing to award 100% for correct solution based on
## t quantiles and 50% for the solution based on normal quantiles
##
## this can be handled as a "verbatim" solution, directly including Moodles
## cloze type:
##   ":NUMERICAL:=2.228:0.1~%50%1.960:0.1#Approximately normal instead of exact t solution."
## where 2.228 is the correct and 1.960 the partially correct solution,
## the tolerance is 0.01 in both cases, and a comment is supplied at the end.
## More details: https://docs.moodle.org/35/en/Embedded_Answers_(Cloze)_question_type

## solution template (note: % have to be escaped as %% for sprintf)
sol <- ":NUMERICAL:=%s:0.1~%%50%%%s:0.1#Aproximación normal en lugar de solución t exacta."

## insert correct and partially correct solutions
sol <- sprintf(sol, c(LBt, UBt), c(LBn, UBn))
@


\begin{question}
Se sospecha que un proveedor sistemáticamente llena por debajo del nivel los bidones de detergente de 5~l.
Se asume que los volúmenes de llenado siguen una distribución normal. Una pequeña muestra de $\Sexpr{n}$
bidones se mide con exactitud. Esto muestra que los bidones contienen en promedio
$\Sexpr{Mean}$~ml. La varianza muestral $s^2_{n-1}$ es igual a $\Sexpr{Var}$.

Determine un intervalo de confianza del $\Sexpr{100 * (1 - alpha)}\%$ para el contenido promedio de
un bidón (en ml).

\begin{answerlist}
\item ¿Cuál es el límite inferior de confianza?
\item ¿Cuál es el límite superior de confianza?
\end{answerlist}
\end{question}


\begin{solution}
El intervalo de confianza del $\Sexpr{100 * (1 - alpha)}\%$ para el contenido promedio $\mu$
en ml viene dado por:
\begin{eqnarray*}
&   & \left[\bar{y} \, - \, t_{n-1;\Sexpr{1-alpha/2}}\sqrt{\frac{s_{n-1}^2}{n}}, \;
  \bar{y} \, + \, t_{n-1;\Sexpr{1-alpha/2}}\sqrt{\frac{s_{n-1}^2}{n}}\right] \\
& = & \left[ \Sexpr{Mean} \, - \, \Sexpr{fact}\sqrt{\frac{\Sexpr{Var}}{\Sexpr{n}}}, \;
             \Sexpr{Mean} \, + \, \Sexpr{fact}\sqrt{\frac{\Sexpr{Var}}{\Sexpr{n}}}\right] \\
& = & \left[\Sexpr{LBt}, \, \Sexpr{UBt}\right].
\end{eqnarray*}

\begin{answerlist}
\item El límite inferior de confianza es $\Sexpr{LBt}$.
\item El límite superior de confianza es $\Sexpr{UBt}$.
\end{answerlist}
\end{solution}


%% META-INFORMATION
%% \extype{cloze}
%% \exclozetype{verbatim|verbatim}
%% \exsolution{\Sexpr{sol[1]}|\Sexpr{sol[2]}}
%% \exname{Intervalo de confianza}
%% \extol{0.01}
\end{document}
