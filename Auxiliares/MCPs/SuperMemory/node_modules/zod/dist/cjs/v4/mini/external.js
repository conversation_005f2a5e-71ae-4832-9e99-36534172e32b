"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.coerce = exports.ZodMiniISODuration = exports.ZodMiniISOTime = exports.ZodMiniISODate = exports.ZodMiniISODateTime = exports.iso = exports.locales = exports.TimePrecision = exports.toJSONSchema = exports.flattenError = exports.formatError = exports.prettifyError = exports.treeifyError = exports.regexes = exports.clone = exports.function = exports.$brand = exports.$input = exports.$output = exports.config = exports.registry = exports.globalRegistry = exports.core = void 0;
exports.core = __importStar(require("zod/v4/core"));
__exportStar(require("./parse.js"), exports);
__exportStar(require("./schemas.js"), exports);
__exportStar(require("./checks.js"), exports);
var core_1 = require("zod/v4/core");
Object.defineProperty(exports, "globalRegistry", { enumerable: true, get: function () { return core_1.globalRegistry; } });
Object.defineProperty(exports, "registry", { enumerable: true, get: function () { return core_1.registry; } });
Object.defineProperty(exports, "config", { enumerable: true, get: function () { return core_1.config; } });
Object.defineProperty(exports, "$output", { enumerable: true, get: function () { return core_1.$output; } });
Object.defineProperty(exports, "$input", { enumerable: true, get: function () { return core_1.$input; } });
Object.defineProperty(exports, "$brand", { enumerable: true, get: function () { return core_1.$brand; } });
Object.defineProperty(exports, "function", { enumerable: true, get: function () { return core_1.function; } });
Object.defineProperty(exports, "clone", { enumerable: true, get: function () { return core_1.clone; } });
Object.defineProperty(exports, "regexes", { enumerable: true, get: function () { return core_1.regexes; } });
Object.defineProperty(exports, "treeifyError", { enumerable: true, get: function () { return core_1.treeifyError; } });
Object.defineProperty(exports, "prettifyError", { enumerable: true, get: function () { return core_1.prettifyError; } });
Object.defineProperty(exports, "formatError", { enumerable: true, get: function () { return core_1.formatError; } });
Object.defineProperty(exports, "flattenError", { enumerable: true, get: function () { return core_1.flattenError; } });
Object.defineProperty(exports, "toJSONSchema", { enumerable: true, get: function () { return core_1.toJSONSchema; } });
Object.defineProperty(exports, "TimePrecision", { enumerable: true, get: function () { return core_1.TimePrecision; } });
exports.locales = __importStar(require("../locales/index.js"));
/** A special constant with type `never` */
// export const NEVER = {} as never;
// iso
exports.iso = __importStar(require("./iso.js"));
var iso_js_1 = require("./iso.js");
Object.defineProperty(exports, "ZodMiniISODateTime", { enumerable: true, get: function () { return iso_js_1.ZodMiniISODateTime; } });
Object.defineProperty(exports, "ZodMiniISODate", { enumerable: true, get: function () { return iso_js_1.ZodMiniISODate; } });
Object.defineProperty(exports, "ZodMiniISOTime", { enumerable: true, get: function () { return iso_js_1.ZodMiniISOTime; } });
Object.defineProperty(exports, "ZodMiniISODuration", { enumerable: true, get: function () { return iso_js_1.ZodMiniISODuration; } });
// coerce
exports.coerce = __importStar(require("./coerce.js"));
