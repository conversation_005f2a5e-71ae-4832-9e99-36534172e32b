# Configuración Global de Contexto - Roo Code

## Referencia Principal de Reglas

**Archivo de Contexto Principal:**
```
/home/<USER>/Documentos/proyecto-r-exams-icfes-matematicas-optimizado/Auxiliares/rules_full/rules_full_v1.md
```

Este archivo contiene:
- Reglas específicas para generación de ejercicios ICFES
- Estructura obligatoria de archivos .Rmd
- Metadatos y configuraciones requeridas
- Patrones de código validados
- Protocolo de corrección de errores
- Referencias a ejemplos funcionales

## Aplicación Universal

Todos los modos de Roo Code deben:
1. **SIEMPRE** consultar primero este archivo de contexto
2. Seguir las estructuras y patrones definidos
3. Usar los ejemplos funcionales como referencia
4. Aplicar las validaciones y pruebas especificadas
5. Mantener coherencia con los metadatos ICFES

## Prioridad de Reglas

1. **PRIMERA PRIORIDAD:** Auxiliares/rules_full/rules_full_v1.md
2. **SEGUNDA PRIORIDAD:** Reglas específicas del modo actual
3. **TERCERA PRIORIDAD:** Reglas generales de SPARC

## Acceso al Contexto

Para todos los modos, el contexto principal se encuentra en:
`../../Auxiliares/rules_full/rules_full_v1.md` (ruta relativa desde .roo/)