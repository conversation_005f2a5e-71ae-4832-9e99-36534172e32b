---
output:
  html_document:
    df_print: paged
    mathjax: true
  pdf_document: 
    latex_engine: xelatex
    keep_tex: true
  word_document: default
header-includes:
- \usepackage[spanish]{babel}
- \usepackage{amsmath}
- \usepackage{fontspec}
- \usepackage{unicode-math}
- \usepackage{graphicx}
- \usepackage{adjustbox}
- \usepackage{tikz}
- \usepackage{pgfplots}
- \usetikzlibrary{3d,babel}
---

```{r setup, include=FALSE}
# Librerías esenciales
library(exams)
library(ggplot2)
library(knitr)
library(reticulate)
library(testthat)
library(data.table)
library(readxl)
library(datasets)

# Configurar Python si es necesario
use_python(Sys.which("python"), required = TRUE)

# Configuración global
typ <- match_exams_device()
options(scipen = 999)
knitr::opts_chunk$set(
  warning = FALSE, 
  message = FALSE,
  fig.keep = 'all',
  dev = c("png", "pdf"),
  dpi = 150,
  echo = FALSE,
  results = "hide",
  fig.pos = "H"
)

# Configuración para chunks de Python
knitr::knit_engines$set(python = function(options) {
  knitr::engine_output(options, options$code, '')
})

# Asegurar que Python esté correctamente configurado
use_python(Sys.which("python"), required = TRUE)
```

```{r DefinicionDeVariables, message=FALSE, warning=FALSE, results='asis'}
options(OutDec = ".")  # Asegurar punto decimal en este chunk

# Establecer semilla aleatoria para reproducibilidad
#set.seed(sample(1:10000, 1))

# Aleatorización de contexto y personajes (un nombre masculino y uno femenino)
nombres_masculinos <- c("Pedro", "Carlos", "Miguel", "Antonio", "Diego", "Sebastián", "Andrés", "Luis", "José", "Francisco")
nombres_femeninos <- c("María", "Ana", "Carmen", "Laura", "Sofía", "Valentina", "Isabella", "Camila", "Daniela", "Alejandra")

# Seleccionar aleatoriamente un nombre masculino y uno femenino
nombre_masculino <- sample(nombres_masculinos, 1)
nombre_femenino <- sample(nombres_femeninos, 1)

# Aleatorizar el orden (quién se menciona primero)
if(sample(c(TRUE, FALSE), 1)) {
  nombre1 <- nombre_masculino
  nombre2 <- nombre_femenino
} else {
  nombre1 <- nombre_femenino
  nombre2 <- nombre_masculino
}

# Aleatorización de contexto habitacional
tipos_vivienda <- c("apartamento", "hogar", "aparta-estudio")
tipo_vivienda <- sample(tipos_vivienda, 1)

# Aleatorizar meses involucrados en el problema
meses_disponibles <- c("Enero", "Febrero", "Marzo", "Abril", "Mayo", "Junio",
                      "Julio", "Agosto", "Septiembre", "Octubre", "Noviembre", "Diciembre")

# Seleccionar 7 meses consecutivos aleatoriamente
inicio_mes <- sample(1:6, 1)  # Asegurar que quepan 7 meses
meses_seleccionados <- meses_disponibles[inicio_mes:(inicio_mes + 6)]

# Asignar roles: mes de la pregunta (posición aleatoria) y mes de la factura (último mes)
posicion_pregunta <- sample(1:6, 1)  # No puede ser el último mes (reservado para factura)
mes_pregunta <- meses_seleccionados[posicion_pregunta]
mes_factura <- meses_seleccionados[7]  # Último mes para la factura

# Crear vector de meses y consumos (mantenemos la gráfica de consumos)
meses <- meses_seleccionados

# Generar consumos base realistas para la gráfica
consumos_base <- numeric(7)
for(i in 1:7) {
  consumos_base[i] <- sample(10:18, 1)  # Consumos realistas
}

consumos <- consumos_base
names(consumos) <- meses

# Generar datos económicos para el cálculo del costo promedio diario
# Generar cobro total del mes de la pregunta (entre $15,000 y $25,000)
cobro_total <- sample(15000:25000, 1)

# Generar valor por servicio fijo (entre $3,000 y $8,000)
valor_fijo <- sample(3000:8000, 1)

# Calcular costo promedio diario
costo_consumo_variable <- cobro_total - valor_fijo
costo_promedio_diario <- round(costo_consumo_variable / 30, 0)

# Calcular respuesta correcta
respuesta_correcta <- costo_promedio_diario

# Generar distractores basados en errores comunes
distractores <- c()

# Distractor 1: No restar el valor fijo
distractor_1 <- round(cobro_total / 30, 0)
if(distractor_1 != respuesta_correcta && distractor_1 > 0) {
  distractores <- c(distractores, distractor_1)
}

# Distractor 2: Dividir entre 31 días en lugar de 30
distractor_2 <- round(costo_consumo_variable / 31, 0)
if(distractor_2 != respuesta_correcta && distractor_2 > 0 && !distractor_2 %in% distractores) {
  distractores <- c(distractores, distractor_2)
}

# Distractor 3: Usar solo el valor fijo dividido entre 30
distractor_3 <- round(valor_fijo / 30, 0)
if(distractor_3 != respuesta_correcta && distractor_3 > 0 && !distractor_3 %in% distractores) {
  distractores <- c(distractores, distractor_3)
}

# Completar distractores con opciones plausibles si es necesario
opciones_adicionales <- c(90, 150, 200, 250, 300, 350, 400, 450, 500, 550, 600, 650, 700, 750, 800, 850, 900, 950)
opciones_adicionales <- opciones_adicionales[opciones_adicionales != respuesta_correcta]

for(opcion in sample(opciones_adicionales)) {
  if(length(distractores) >= 3) break
  if(!opcion %in% distractores) {
    distractores <- c(distractores, opcion)
  }
}

# Asegurar exactamente 3 distractores
distractores <- distractores[1:3]

# Crear opciones finales
opciones <- c(respuesta_correcta, distractores)
opciones_texto <- opciones

# Vector de solución para r-exams
solucion <- c(TRUE, FALSE, FALSE, FALSE)

# Mezclar opciones aleatoriamente
orden_aleatorio <- sample(1:4)
opciones_mezcladas <- opciones_texto[orden_aleatorio]
solucion_mezclada <- solucion[orden_aleatorio]

# Detectar formato de salida para ajustes posteriores
formatos_moodle <- c("exams2moodle", "exams2qti12", "exams2qti21", "exams2openolat")
es_moodle <- (match_exams_call() %in% formatos_moodle)

# PRUEBAS DE VALIDACIÓN MATEMÁTICA
test_that("Validación de datos generados", {
  expect_true(cobro_total >= 15000 && cobro_total <= 25000)
  expect_true(valor_fijo >= 3000 && valor_fijo <= 8000)
  expect_true(cobro_total > valor_fijo)  # El cobro total debe ser mayor al valor fijo
  expect_true(costo_promedio_diario > 0)
  expect_true(all(consumos > 0 & consumos <= 20))
  expect_equal(length(opciones), 4)
  expect_equal(length(unique(opciones)), 4)  # Todas las opciones deben ser diferentes
  expect_true(nombre_masculino %in% nombres_masculinos)
  expect_true(nombre_femenino %in% nombres_femeninos)
  expect_true(nombre1 %in% c(nombre_masculino, nombre_femenino))
  expect_true(nombre2 %in% c(nombre_masculino, nombre_femenino))
  expect_true(nombre1 != nombre2)  # Los nombres deben ser diferentes
  expect_true(mes_pregunta %in% meses)
  expect_true(mes_factura %in% meses)
  expect_true(mes_pregunta != mes_factura)  # Los meses deben ser diferentes
})

test_that("Validación de coherencia matemática", {
  # Verificar que el cálculo del costo promedio diario es correcto
  costo_calculado <- round((cobro_total - valor_fijo) / 30, 0)
  expect_equal(costo_promedio_diario, costo_calculado)

  # Verificar que la respuesta correcta está en las opciones
  expect_true(respuesta_correcta %in% opciones)

  # Verificar que los distractores son diferentes de la respuesta correcta
  expect_true(all(distractores != respuesta_correcta))
})

test_that("Validación de coherencia económica", {
  # Verificar que el costo consumo variable es correcto
  expect_equal(costo_consumo_variable, cobro_total - valor_fijo)

  # Verificar que el costo promedio diario es razonable
  expect_true(costo_promedio_diario >= 100 && costo_promedio_diario <= 1000)

  # Verificar que los consumos son razonables
  expect_true(all(consumos >= 10 & consumos <= 18))
})
```

```{r generar_grafico_barras_python, message=FALSE, warning=FALSE}
options(OutDec = ".")  # Asegurar punto decimal en este chunk

# Preparar datos para Python
meses_python <- paste0("['", paste(meses, collapse="', '"), "']")
consumos_python <- paste0("[", paste(consumos, collapse=", "), "]")

# Código Python para generar el gráfico de barras
codigo_python <- paste0("
import matplotlib
matplotlib.use('Agg')  # Usar backend no interactivo
import matplotlib.pyplot as plt
import numpy as np
import random

# Datos del gráfico
meses = ", meses_python, "
consumos = ", consumos_python, "

# Paletas de colores variadas
paletas_colores = [
    ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'],  # Colores suaves
    ['#E74C3C', '#3498DB', '#2ECC71', '#F39C12', '#9B59B6', '#1ABC9C', '#E67E22'],  # Colores vibrantes
    ['#FF7675', '#74B9FF', '#00B894', '#FDCB6E', '#6C5CE7', '#A29BFE', '#FD79A8'],  # Colores modernos
    ['#D63031', '#0984E3', '#00B894', '#E17055', '#A29BFE', '#FD79A8', '#FDCB6E'],  # Colores intensos
    ['#FF9FF3', '#54A0FF', '#5F27CD', '#00D2D3', '#FF9F43', '#10AC84', '#EE5A24'],  # Colores dinámicos
    ['#FF6348', '#2ED573', '#1E90FF', '#FFA502', '#FF6B81', '#7BED9F', '#70A1FF']   # Colores equilibrados
]

# Seleccionar paleta aleatoria
paleta_seleccionada = random.choice(paletas_colores)

# Asignar colores aleatorios a cada barra
colores = []
for i, mes in enumerate(meses):
    if mes == '", mes_pregunta, "':
        # Color más oscuro para el mes de la pregunta
        colores.append('#2C3E50')  # Color oscuro universal para destacar
    else:
        colores.append(paleta_seleccionada[i % len(paleta_seleccionada)])

# Crear figura
plt.figure(figsize=(8, 5))

# Crear gráfico de barras
barras = plt.bar(meses, consumos, color=colores, edgecolor='black', linewidth=1, width=0.7)

# Configuración del gráfico
plt.xlabel('Mes', fontsize=11, fontweight='bold')
plt.ylabel('Consumo en metros cúbicos', fontsize=11, fontweight='bold')
plt.xticks(rotation=45, ha='right', fontsize=10)
plt.yticks(fontsize=10)

# Configurar límites del eje Y
plt.ylim(0, max(consumos) + 1)

# Añadir valores sobre las barras
for i, (mes, consumo) in enumerate(zip(meses, consumos)):
    plt.text(i, consumo + 0.1, str(consumo), ha='center', va='bottom', fontweight='bold', fontsize=9)

# Configurar grilla
plt.grid(True, axis='y', linestyle='--', alpha=0.7)

# Ajustar diseño
plt.tight_layout()

# Guardar en múltiples formatos para compatibilidad
plt.savefig('grafico_consumo_gas.png', dpi=150, bbox_inches='tight', transparent=False)
plt.savefig('grafico_consumo_gas.pdf', dpi=150, bbox_inches='tight', transparent=False)
plt.close()
")

# Ejecutar código Python para generar la figura
py_run_string(codigo_python)

# Validar que el gráfico se creó correctamente
test_that("Validación del gráfico Python", {
  expect_true(file.exists("grafico_consumo_gas.png"))
  expect_equal(length(meses), 7)  # 7 meses
  expect_equal(length(consumos), 7)  # 7 valores de consumo
})
```

Question
========

`r nombre1` y `r nombre2` viven en un `r tipo_vivienda` y comparten el pago de los gastos. Para saber cuál es el costo promedio del consumo diario de gas natural, se resta del cobro total de ese mes el valor por servicio fijo, y luego se divide entre 30, que es el número de días que se utiliza para facturar un mes. En `r tolower(mes_pregunta)` el cobro total fue de $`r format(cobro_total, big.mark = ".", decimal.mark = ",")` y el valor por servicio fijo fue de $`r format(valor_fijo, big.mark = ".", decimal.mark = ",")`. La gráfica muestra el consumo histórico en metros cúbicos de ese servicio.

```{r mostrar_grafico, echo=FALSE, results='asis', fig.align="center"}
# Mostrar la imagen del gráfico generado con Python
# Ajustar tamaño según el formato de salida
if (es_moodle) {
  cat("![](grafico_consumo_gas.png){width=70%}")
} else {
  cat("![](grafico_consumo_gas.png){width=80%}")
}
```

¿Cuál es el costo promedio aproximado, en pesos, del consumo diario de `r tolower(mes_pregunta)`?

Answerlist
----------
* `r opciones_mezcladas[1]`
* `r opciones_mezcladas[2]`
* `r opciones_mezcladas[3]`
* `r opciones_mezcladas[4]`

Solution
========

### Análisis del problema

Este problema requiere **interpretación de datos económicos** y **cálculo de costo promedio diario**. Los pasos son:

1. **Identificar los datos económicos** del enunciado
2. **Aplicar la fórmula de costo promedio diario** según las instrucciones
3. **Realizar el cálculo** correctamente

### Paso 1: Datos del problema

Según el enunciado, para `r tolower(mes_pregunta)`:

- **Cobro total del mes:** $`r format(cobro_total, big.mark = ".", decimal.mark = ",")`
- **Valor por servicio fijo:** $`r format(valor_fijo, big.mark = ".", decimal.mark = ",")`
- **Número de días para facturar:** 30 días

### Paso 2: Aplicación de la fórmula

Según las instrucciones del problema:

**Costo promedio diario = (Cobro total - Valor por servicio fijo) ÷ 30**

### Paso 3: Cálculo paso a paso

1. **Calcular el costo variable:**
   Costo variable = $`r format(cobro_total, big.mark = ".", decimal.mark = ",")` - $`r format(valor_fijo, big.mark = ".", decimal.mark = ",")` = $`r format(costo_consumo_variable, big.mark = ".", decimal.mark = ",")`

2. **Calcular el costo promedio diario:**
   Costo promedio diario = $`r format(costo_consumo_variable, big.mark = ".", decimal.mark = ",")` ÷ 30 = $`r costo_promedio_diario`

### Verificación y análisis de distractores

**Respuesta correcta:** $`r costo_promedio_diario`

```{r analisis_distractores, echo=FALSE, results='asis'}
cat("**Análisis de errores conceptuales comunes:**\n\n")

# Identificar qué tipo de distractores se generaron
for(i in 1:length(distractores)) {
  distractor <- distractores[i]

  if(distractor == round(cobro_total / 30, 0)) {
    cat(paste0("- **$", distractor, "**: Error conceptual - no restar el valor por servicio fijo\n"))
  } else if(distractor == round(costo_consumo_variable / 31, 0)) {
    cat(paste0("- **$", distractor, "**: Error de cálculo - dividir entre 31 días en lugar de 30\n"))
  } else if(distractor == round(valor_fijo / 30, 0)) {
    cat(paste0("- **$", distractor, "**: Error conceptual - usar solo el valor fijo dividido entre 30\n"))
  } else {
    cat(paste0("- **$", distractor, "**: Error de cálculo - aplicar incorrectamente la fórmula\n"))
  }
}
```

### Conclusión

El costo promedio diario del consumo de `r tolower(mes_pregunta)` es de **$`r costo_promedio_diario`**.

Esta respuesta es coherente porque:

- Se basa en los datos económicos proporcionados en el enunciado
- Aplica correctamente la fórmula indicada: (Cobro total - Valor fijo) ÷ 30
- El resultado es razonable para un costo diario de consumo de gas natural

**Verificación del cálculo**:

- Costo variable: $`r format(cobro_total, big.mark = ".", decimal.mark = ",")` - $`r format(valor_fijo, big.mark = ".", decimal.mark = ",")` = $`r format(costo_consumo_variable, big.mark = ".", decimal.mark = ",")`
- Costo promedio diario: $`r format(costo_consumo_variable, big.mark = ".", decimal.mark = ",")` ÷ 30 = $`r costo_promedio_diario`

```{r solucion, echo=FALSE, results="asis"}
answerlist(ifelse(solucion_mezclada, "Correcto", "Incorrecto"), markup = "markdown")
```

Meta-information
================
exname: Consumo Gas Natural Costo Promedio Diario
extype: schoice
exsolution: `r paste(as.integer(solucion_mezclada), collapse="")`
exshuffle: TRUE
exsection: Aritmética|Cálculo de promedios|Análisis económico|Operaciones básicas
exextra[Type]: Cálculo
exextra[Program]: R
exextra[Language]: es
exextra[Level]: 2
exextra[Competencia]: Formulación y ejecución
exextra[Componente]: Numérico variacional
exextra[Contexto]: Familiar
exextra[Dificultad]: Media
