% Options for packages loaded elsewhere
\PassOptionsToPackage{unicode}{hyperref}
\PassOptionsToPackage{hyphens}{url}
%
\documentclass[
]{article}
\usepackage{amsmath,amssymb}
\usepackage{iftex}
\ifPDFTeX
  \usepackage[T1]{fontenc}
  \usepackage[utf8]{inputenc}
  \usepackage{textcomp} % provide euro and other symbols
\else % if luatex or xetex
  \usepackage{unicode-math} % this also loads fontspec
  \defaultfontfeatures{Scale=MatchLowercase}
  \defaultfontfeatures[\rmfamily]{Ligatures=TeX,Scale=1}
\fi
\usepackage{lmodern}
\ifPDFTeX\else
  % xetex/luatex font selection
\fi
% Use upquote if available, for straight quotes in verbatim environments
\IfFileExists{upquote.sty}{\usepackage{upquote}}{}
\IfFileExists{microtype.sty}{% use microtype if available
  \usepackage[]{microtype}
  \UseMicrotypeSet[protrusion]{basicmath} % disable protrusion for tt fonts
}{}
\makeatletter
\@ifundefined{KOMAClassName}{% if non-KOMA class
  \IfFileExists{parskip.sty}{%
    \usepackage{parskip}
  }{% else
    \setlength{\parindent}{0pt}
    \setlength{\parskip}{6pt plus 2pt minus 1pt}}
}{% if KOMA class
  \KOMAoptions{parskip=half}}
\makeatother
\usepackage{xcolor}
\usepackage[margin=1in]{geometry}
\usepackage{color}
\usepackage{fancyvrb}
\newcommand{\VerbBar}{|}
\newcommand{\VERB}{\Verb[commandchars=\\\{\}]}
\DefineVerbatimEnvironment{Highlighting}{Verbatim}{commandchars=\\\{\}}
% Add ',fontsize=\small' for more characters per line
\usepackage{framed}
\definecolor{shadecolor}{RGB}{248,248,248}
\newenvironment{Shaded}{\begin{snugshade}}{\end{snugshade}}
\newcommand{\AlertTok}[1]{\textcolor[rgb]{0.94,0.16,0.16}{#1}}
\newcommand{\AnnotationTok}[1]{\textcolor[rgb]{0.56,0.35,0.01}{\textbf{\textit{#1}}}}
\newcommand{\AttributeTok}[1]{\textcolor[rgb]{0.13,0.29,0.53}{#1}}
\newcommand{\BaseNTok}[1]{\textcolor[rgb]{0.00,0.00,0.81}{#1}}
\newcommand{\BuiltInTok}[1]{#1}
\newcommand{\CharTok}[1]{\textcolor[rgb]{0.31,0.60,0.02}{#1}}
\newcommand{\CommentTok}[1]{\textcolor[rgb]{0.56,0.35,0.01}{\textit{#1}}}
\newcommand{\CommentVarTok}[1]{\textcolor[rgb]{0.56,0.35,0.01}{\textbf{\textit{#1}}}}
\newcommand{\ConstantTok}[1]{\textcolor[rgb]{0.56,0.35,0.01}{#1}}
\newcommand{\ControlFlowTok}[1]{\textcolor[rgb]{0.13,0.29,0.53}{\textbf{#1}}}
\newcommand{\DataTypeTok}[1]{\textcolor[rgb]{0.13,0.29,0.53}{#1}}
\newcommand{\DecValTok}[1]{\textcolor[rgb]{0.00,0.00,0.81}{#1}}
\newcommand{\DocumentationTok}[1]{\textcolor[rgb]{0.56,0.35,0.01}{\textbf{\textit{#1}}}}
\newcommand{\ErrorTok}[1]{\textcolor[rgb]{0.64,0.00,0.00}{\textbf{#1}}}
\newcommand{\ExtensionTok}[1]{#1}
\newcommand{\FloatTok}[1]{\textcolor[rgb]{0.00,0.00,0.81}{#1}}
\newcommand{\FunctionTok}[1]{\textcolor[rgb]{0.13,0.29,0.53}{\textbf{#1}}}
\newcommand{\ImportTok}[1]{#1}
\newcommand{\InformationTok}[1]{\textcolor[rgb]{0.56,0.35,0.01}{\textbf{\textit{#1}}}}
\newcommand{\KeywordTok}[1]{\textcolor[rgb]{0.13,0.29,0.53}{\textbf{#1}}}
\newcommand{\NormalTok}[1]{#1}
\newcommand{\OperatorTok}[1]{\textcolor[rgb]{0.81,0.36,0.00}{\textbf{#1}}}
\newcommand{\OtherTok}[1]{\textcolor[rgb]{0.56,0.35,0.01}{#1}}
\newcommand{\PreprocessorTok}[1]{\textcolor[rgb]{0.56,0.35,0.01}{\textit{#1}}}
\newcommand{\RegionMarkerTok}[1]{#1}
\newcommand{\SpecialCharTok}[1]{\textcolor[rgb]{0.81,0.36,0.00}{\textbf{#1}}}
\newcommand{\SpecialStringTok}[1]{\textcolor[rgb]{0.31,0.60,0.02}{#1}}
\newcommand{\StringTok}[1]{\textcolor[rgb]{0.31,0.60,0.02}{#1}}
\newcommand{\VariableTok}[1]{\textcolor[rgb]{0.00,0.00,0.00}{#1}}
\newcommand{\VerbatimStringTok}[1]{\textcolor[rgb]{0.31,0.60,0.02}{#1}}
\newcommand{\WarningTok}[1]{\textcolor[rgb]{0.56,0.35,0.01}{\textbf{\textit{#1}}}}
\usepackage{graphicx}
\makeatletter
\newsavebox\pandoc@box
\newcommand*\pandocbounded[1]{% scales image to fit in text height/width
  \sbox\pandoc@box{#1}%
  \Gscale@div\@tempa{\textheight}{\dimexpr\ht\pandoc@box+\dp\pandoc@box\relax}%
  \Gscale@div\@tempb{\linewidth}{\wd\pandoc@box}%
  \ifdim\@tempb\p@<\@tempa\p@\let\@tempa\@tempb\fi% select the smaller of both
  \ifdim\@tempa\p@<\p@\scalebox{\@tempa}{\usebox\pandoc@box}%
  \else\usebox{\pandoc@box}%
  \fi%
}
% Set default figure placement to htbp
\def\fps@figure{htbp}
\makeatother
\setlength{\emergencystretch}{3em} % prevent overfull lines
\providecommand{\tightlist}{%
  \setlength{\itemsep}{0pt}\setlength{\parskip}{0pt}}
\setcounter{secnumdepth}{-\maxdimen} % remove section numbering
\usepackage{graphicx}
\usepackage{float}
\usepackage{bookmark}
\IfFileExists{xurl.sty}{\usepackage{xurl}}{} % add URL line breaks if available
\urlstyle{same}
\hypersetup{
  hidelinks,
  pdfcreator={LaTeX via pandoc}}

\author{}
\date{\vspace{-2.5em}}

\begin{document}

\begin{Shaded}
\begin{Highlighting}[]
\FunctionTok{options}\NormalTok{(}\AttributeTok{OutDec =} \StringTok{"."}\NormalTok{)  }\CommentTok{\# Asegurar punto decimal en este chunk}

\CommentTok{\# Establecer semilla aleatoria}
\FunctionTok{set.seed}\NormalTok{(}\FunctionTok{sample}\NormalTok{(}\DecValTok{1}\SpecialCharTok{:}\DecValTok{10000}\NormalTok{, }\DecValTok{1}\NormalTok{))}

\CommentTok{\# Aleatorización del contexto del problema}
\NormalTok{contextos }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(}
  \StringTok{"empresa"}\NormalTok{, }\StringTok{"organización"}\NormalTok{, }\StringTok{"compañía"}\NormalTok{, }\StringTok{"institución"}\NormalTok{, }\StringTok{"corporación"}\NormalTok{,}
  \StringTok{"entidad"}\NormalTok{, }\StringTok{"firma"}\NormalTok{, }\StringTok{"negocio"}\NormalTok{, }\StringTok{"sociedad"}\NormalTok{, }\StringTok{"grupo empresarial"}
\NormalTok{)}
\NormalTok{contexto }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(contextos, }\DecValTok{1}\NormalTok{)}

\CommentTok{\# Aleatorización de los tipos de bienes}
\NormalTok{tipos\_bien1 }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(}\StringTok{"carro"}\NormalTok{, }\StringTok{"auto"}\NormalTok{, }\StringTok{"vehículo"}\NormalTok{, }\StringTok{"automóvil"}\NormalTok{, }\StringTok{"coche"}\NormalTok{)}
\NormalTok{tipos\_bien2 }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(}\StringTok{"casa"}\NormalTok{, }\StringTok{"vivienda"}\NormalTok{, }\StringTok{"residencia"}\NormalTok{, }\StringTok{"hogar"}\NormalTok{, }\StringTok{"domicilio"}\NormalTok{)}
\NormalTok{tipos\_bien3 }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(}\StringTok{"apartamento"}\NormalTok{, }\StringTok{"departamento"}\NormalTok{, }\StringTok{"piso"}\NormalTok{, }\StringTok{"condominio"}\NormalTok{)}

\NormalTok{bien1 }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(tipos\_bien1, }\DecValTok{1}\NormalTok{)}
\NormalTok{bien2 }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(tipos\_bien2, }\DecValTok{1}\NormalTok{)}
\NormalTok{bien3 }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(tipos\_bien3, }\DecValTok{1}\NormalTok{)}

\CommentTok{\# Aleatorización de términos para el enunciado}
\NormalTok{terminos\_encuesta }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(}\StringTok{"encuesta"}\NormalTok{, }\StringTok{"sondeo"}\NormalTok{, }\StringTok{"estudio"}\NormalTok{, }\StringTok{"investigación"}\NormalTok{, }\StringTok{"consulta"}\NormalTok{)}
\NormalTok{termino\_encuesta }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(terminos\_encuesta, }\DecValTok{1}\NormalTok{)}

\NormalTok{terminos\_personas }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(}\StringTok{"personas"}\NormalTok{, }\StringTok{"empleados"}\NormalTok{, }\StringTok{"trabajadores"}\NormalTok{, }\StringTok{"colaboradores"}\NormalTok{, }\StringTok{"miembros"}\NormalTok{)}
\NormalTok{termino\_personas }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(terminos\_personas, }\DecValTok{1}\NormalTok{)}

\NormalTok{terminos\_bienes }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(}\StringTok{"bienes"}\NormalTok{, }\StringTok{"posesiones"}\NormalTok{, }\StringTok{"propiedades"}\NormalTok{, }\StringTok{"activos"}\NormalTok{, }\StringTok{"patrimonios"}\NormalTok{)}
\NormalTok{termino\_bienes }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(terminos\_bienes, }\DecValTok{1}\NormalTok{)}

\NormalTok{terminos\_resultados }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(}\StringTok{"resultados"}\NormalTok{, }\StringTok{"datos"}\NormalTok{, }\StringTok{"estadísticas"}\NormalTok{, }\StringTok{"cifras"}\NormalTok{)}
\NormalTok{termino\_resultados }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(terminos\_resultados, }\DecValTok{1}\NormalTok{)}

\CommentTok{\# Aleatorización de colores para el gráfico circular (paletas oscuras)}
\NormalTok{paleta\_colores }\OtherTok{\textless{}{-}} \FunctionTok{list}\NormalTok{(}
  \FunctionTok{c}\NormalTok{(}\StringTok{"\#C62828"}\NormalTok{, }\StringTok{"\#2E7D32"}\NormalTok{, }\StringTok{"\#1565C0"}\NormalTok{, }\StringTok{"\#EF6C00"}\NormalTok{, }\StringTok{"\#6A1B9A"}\NormalTok{),  }\CommentTok{\# Paleta oscura 1}
  \FunctionTok{c}\NormalTok{(}\StringTok{"\#D32F2F"}\NormalTok{, }\StringTok{"\#388E3C"}\NormalTok{, }\StringTok{"\#1976D2"}\NormalTok{, }\StringTok{"\#F57C00"}\NormalTok{, }\StringTok{"\#7B1FA2"}\NormalTok{),  }\CommentTok{\# Paleta saturada}
  \FunctionTok{c}\NormalTok{(}\StringTok{"\#B71C1C"}\NormalTok{, }\StringTok{"\#1B5E20"}\NormalTok{, }\StringTok{"\#0D47A1"}\NormalTok{, }\StringTok{"\#E65100"}\NormalTok{, }\StringTok{"\#4A148C"}\NormalTok{),  }\CommentTok{\# Paleta oscura 2}
  \FunctionTok{c}\NormalTok{(}\StringTok{"\#AD1457"}\NormalTok{, }\StringTok{"\#00695C"}\NormalTok{, }\StringTok{"\#283593"}\NormalTok{, }\StringTok{"\#FF6F00"}\NormalTok{, }\StringTok{"\#4A148C"}\NormalTok{),  }\CommentTok{\# Paleta oscura 3}
  \FunctionTok{c}\NormalTok{(}\StringTok{"\#880E4F"}\NormalTok{, }\StringTok{"\#1B5E20"}\NormalTok{, }\StringTok{"\#0D47A1"}\NormalTok{, }\StringTok{"\#BF360C"}\NormalTok{, }\StringTok{"\#4A148C"}\NormalTok{)   }\CommentTok{\# Paleta oscura 4}
\NormalTok{)}
\NormalTok{paleta\_seleccionada }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(paleta\_colores, }\DecValTok{1}\NormalTok{)[[}\DecValTok{1}\NormalTok{]]}

\CommentTok{\# Aleatorización de los porcentajes manteniendo coherencia matemática}
\CommentTok{\# Generación de porcentajes iniciales para cada categoría}
\NormalTok{set\_porcentajes }\OtherTok{\textless{}{-}} \ControlFlowTok{function}\NormalTok{() \{}
  \ControlFlowTok{repeat}\NormalTok{ \{}
    \CommentTok{\# Generar valores base aleatorios}
\NormalTok{    p\_bien1\_bien3 }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(}\DecValTok{15}\SpecialCharTok{:}\DecValTok{35}\NormalTok{, }\DecValTok{1}\NormalTok{)  }\CommentTok{\# Porcentaje de bien1 y bien3}
\NormalTok{    p\_solo\_bien3 }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(}\DecValTok{15}\SpecialCharTok{:}\DecValTok{30}\NormalTok{, }\DecValTok{1}\NormalTok{)   }\CommentTok{\# Porcentaje solo bien3}
\NormalTok{    p\_solo\_bien2 }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(}\DecValTok{25}\SpecialCharTok{:}\DecValTok{40}\NormalTok{, }\DecValTok{1}\NormalTok{)   }\CommentTok{\# Porcentaje solo bien2}
\NormalTok{    p\_solo\_bien1 }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(}\DecValTok{5}\SpecialCharTok{:}\DecValTok{10}\NormalTok{, }\DecValTok{1}\NormalTok{)    }\CommentTok{\# Porcentaje solo bien1}

    \CommentTok{\# Calcular el porcentaje restante para bien1 y bien2}
\NormalTok{    p\_bien1\_bien2 }\OtherTok{\textless{}{-}} \DecValTok{100} \SpecialCharTok{{-}}\NormalTok{ (p\_bien1\_bien3 }\SpecialCharTok{+}\NormalTok{ p\_solo\_bien3 }\SpecialCharTok{+}\NormalTok{ p\_solo\_bien2 }\SpecialCharTok{+}\NormalTok{ p\_solo\_bien1)}

    \CommentTok{\# Validar que el porcentaje restante sea positivo y razonable}
    \ControlFlowTok{if}\NormalTok{ (p\_bien1\_bien2 }\SpecialCharTok{\textgreater{}=} \DecValTok{10} \SpecialCharTok{\&\&}\NormalTok{ p\_bien1\_bien2 }\SpecialCharTok{\textless{}=} \DecValTok{25}\NormalTok{) \{}
      \FunctionTok{return}\NormalTok{(}\FunctionTok{c}\NormalTok{(p\_bien1\_bien3, p\_solo\_bien3, p\_solo\_bien2, p\_solo\_bien1, p\_bien1\_bien2))}
\NormalTok{    \}}
\NormalTok{  \}}
\NormalTok{\}}

\CommentTok{\# Obtener porcentajes válidos}
\NormalTok{porcentajes }\OtherTok{\textless{}{-}} \FunctionTok{set\_porcentajes}\NormalTok{()}

\NormalTok{p\_bien1\_bien3 }\OtherTok{\textless{}{-}}\NormalTok{ porcentajes[}\DecValTok{1}\NormalTok{]  }\CommentTok{\# Porcentaje de bien1 y bien3}
\NormalTok{p\_solo\_bien3 }\OtherTok{\textless{}{-}}\NormalTok{ porcentajes[}\DecValTok{2}\NormalTok{]   }\CommentTok{\# Porcentaje solo bien3}
\NormalTok{p\_solo\_bien2 }\OtherTok{\textless{}{-}}\NormalTok{ porcentajes[}\DecValTok{3}\NormalTok{]   }\CommentTok{\# Porcentaje solo bien2}
\NormalTok{p\_solo\_bien1 }\OtherTok{\textless{}{-}}\NormalTok{ porcentajes[}\DecValTok{4}\NormalTok{]   }\CommentTok{\# Porcentaje solo bien1}
\NormalTok{p\_bien1\_bien2 }\OtherTok{\textless{}{-}}\NormalTok{ porcentajes[}\DecValTok{5}\NormalTok{]  }\CommentTok{\# Porcentaje de bien1 y bien2}

\CommentTok{\# Verificar que suman 100\%}
\FunctionTok{test\_that}\NormalTok{(}\StringTok{"Los porcentajes suman 100\%"}\NormalTok{, \{}
  \FunctionTok{expect\_equal}\NormalTok{(}\FunctionTok{sum}\NormalTok{(porcentajes), }\DecValTok{100}\NormalTok{)}
\NormalTok{\})}
\end{Highlighting}
\end{Shaded}

Test passed

\begin{Shaded}
\begin{Highlighting}[]
\CommentTok{\# Valor conocido para el cálculo: Número de personas con bien1 y bien3}
\NormalTok{personas\_bien1\_bien3 }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(}\FunctionTok{c}\NormalTok{(}\DecValTok{72}\NormalTok{, }\DecValTok{96}\NormalTok{, }\DecValTok{120}\NormalTok{, }\DecValTok{144}\NormalTok{, }\DecValTok{168}\NormalTok{, }\DecValTok{192}\NormalTok{, }\DecValTok{216}\NormalTok{, }\DecValTok{240}\NormalTok{), }\DecValTok{1}\NormalTok{)}

\CommentTok{\# Calcular el total de personas}
\NormalTok{total\_personas }\OtherTok{\textless{}{-}} \FunctionTok{round}\NormalTok{(personas\_bien1\_bien3 }\SpecialCharTok{*} \DecValTok{100} \SpecialCharTok{/}\NormalTok{ p\_bien1\_bien3)}

\CommentTok{\# Calcular el número de personas en cada categoría}
\NormalTok{personas\_solo\_bien3 }\OtherTok{\textless{}{-}} \FunctionTok{round}\NormalTok{(total\_personas }\SpecialCharTok{*}\NormalTok{ p\_solo\_bien3 }\SpecialCharTok{/} \DecValTok{100}\NormalTok{)}
\NormalTok{personas\_solo\_bien2 }\OtherTok{\textless{}{-}} \FunctionTok{round}\NormalTok{(total\_personas }\SpecialCharTok{*}\NormalTok{ p\_solo\_bien2 }\SpecialCharTok{/} \DecValTok{100}\NormalTok{)}
\NormalTok{personas\_solo\_bien1 }\OtherTok{\textless{}{-}} \FunctionTok{round}\NormalTok{(total\_personas }\SpecialCharTok{*}\NormalTok{ p\_solo\_bien1 }\SpecialCharTok{/} \DecValTok{100}\NormalTok{)}
\NormalTok{personas\_bien1\_bien2 }\OtherTok{\textless{}{-}} \FunctionTok{round}\NormalTok{(total\_personas }\SpecialCharTok{*}\NormalTok{ p\_bien1\_bien2 }\SpecialCharTok{/} \DecValTok{100}\NormalTok{)}

\CommentTok{\# Recalcular personas\_bien1\_bien3 para asegurar que el total sea correcto}
\NormalTok{personas\_bien1\_bien3 }\OtherTok{\textless{}{-}}\NormalTok{ total\_personas }\SpecialCharTok{{-}}\NormalTok{ (personas\_solo\_bien3 }\SpecialCharTok{+}\NormalTok{ personas\_solo\_bien2 }\SpecialCharTok{+}\NormalTok{ personas\_solo\_bien1 }\SpecialCharTok{+}\NormalTok{ personas\_bien1\_bien2)}

\CommentTok{\# Asegurar que todos los valores son positivos y razonables}
\NormalTok{personas }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(personas\_bien1\_bien3, personas\_solo\_bien3, personas\_solo\_bien2, personas\_solo\_bien1, personas\_bien1\_bien2)}
\FunctionTok{test\_that}\NormalTok{(}\StringTok{"Todas las categorías tienen al menos una persona"}\NormalTok{, \{}
  \FunctionTok{expect\_true}\NormalTok{(}\FunctionTok{all}\NormalTok{(personas }\SpecialCharTok{\textgreater{}} \DecValTok{0}\NormalTok{))}
\NormalTok{\})}
\end{Highlighting}
\end{Shaded}

Test passed

\begin{Shaded}
\begin{Highlighting}[]
\CommentTok{\# Asegurar que la suma de todas las categorías es igual al total}
\FunctionTok{test\_that}\NormalTok{(}\StringTok{"La suma de todas las categorías es igual al total"}\NormalTok{, \{}
  \FunctionTok{expect\_equal}\NormalTok{(}\FunctionTok{sum}\NormalTok{(personas), total\_personas)}
\NormalTok{\})}
\end{Highlighting}
\end{Shaded}

Test passed

\begin{Shaded}
\begin{Highlighting}[]
\CommentTok{\# Recalcular los porcentajes reales basados en los números de personas (para mantener coherencia)}
\NormalTok{p\_bien1\_bien3 }\OtherTok{\textless{}{-}} \FunctionTok{round}\NormalTok{(personas\_bien1\_bien3 }\SpecialCharTok{/}\NormalTok{ total\_personas }\SpecialCharTok{*} \DecValTok{100}\NormalTok{)}
\NormalTok{p\_solo\_bien3 }\OtherTok{\textless{}{-}} \FunctionTok{round}\NormalTok{(personas\_solo\_bien3 }\SpecialCharTok{/}\NormalTok{ total\_personas }\SpecialCharTok{*} \DecValTok{100}\NormalTok{)}
\NormalTok{p\_solo\_bien2 }\OtherTok{\textless{}{-}} \FunctionTok{round}\NormalTok{(personas\_solo\_bien2 }\SpecialCharTok{/}\NormalTok{ total\_personas }\SpecialCharTok{*} \DecValTok{100}\NormalTok{)}
\NormalTok{p\_solo\_bien1 }\OtherTok{\textless{}{-}} \FunctionTok{round}\NormalTok{(personas\_solo\_bien1 }\SpecialCharTok{/}\NormalTok{ total\_personas }\SpecialCharTok{*} \DecValTok{100}\NormalTok{)}
\NormalTok{p\_bien1\_bien2 }\OtherTok{\textless{}{-}} \FunctionTok{round}\NormalTok{(personas\_bien1\_bien2 }\SpecialCharTok{/}\NormalTok{ total\_personas }\SpecialCharTok{*} \DecValTok{100}\NormalTok{)}

\CommentTok{\# Ajustar para asegurar que suman 100\%}
\NormalTok{total\_porcentaje }\OtherTok{\textless{}{-}}\NormalTok{ p\_bien1\_bien3 }\SpecialCharTok{+}\NormalTok{ p\_solo\_bien3 }\SpecialCharTok{+}\NormalTok{ p\_solo\_bien2 }\SpecialCharTok{+}\NormalTok{ p\_solo\_bien1 }\SpecialCharTok{+}\NormalTok{ p\_bien1\_bien2}
\ControlFlowTok{if}\NormalTok{ (total\_porcentaje }\SpecialCharTok{\textgreater{}} \DecValTok{100}\NormalTok{) \{}
  \CommentTok{\# Si suman más de 100, restar la diferencia de la categoría más grande}
\NormalTok{  max\_idx }\OtherTok{\textless{}{-}} \FunctionTok{which.max}\NormalTok{(porcentajes)}
\NormalTok{  porcentajes[max\_idx] }\OtherTok{\textless{}{-}}\NormalTok{ porcentajes[max\_idx] }\SpecialCharTok{{-}}\NormalTok{ (total\_porcentaje }\SpecialCharTok{{-}} \DecValTok{100}\NormalTok{)}
\NormalTok{\} }\ControlFlowTok{else} \ControlFlowTok{if}\NormalTok{ (total\_porcentaje }\SpecialCharTok{\textless{}} \DecValTok{100}\NormalTok{) \{}
  \CommentTok{\# Si suman menos de 100, añadir la diferencia a la categoría más pequeña}
\NormalTok{  min\_idx }\OtherTok{\textless{}{-}} \FunctionTok{which.min}\NormalTok{(porcentajes)}
\NormalTok{  porcentajes[min\_idx] }\OtherTok{\textless{}{-}}\NormalTok{ porcentajes[min\_idx] }\SpecialCharTok{+}\NormalTok{ (}\DecValTok{100} \SpecialCharTok{{-}}\NormalTok{ total\_porcentaje)}
\NormalTok{\}}

\NormalTok{p\_bien1\_bien3 }\OtherTok{\textless{}{-}}\NormalTok{ porcentajes[}\DecValTok{1}\NormalTok{]}
\NormalTok{p\_solo\_bien3 }\OtherTok{\textless{}{-}}\NormalTok{ porcentajes[}\DecValTok{2}\NormalTok{]}
\NormalTok{p\_solo\_bien2 }\OtherTok{\textless{}{-}}\NormalTok{ porcentajes[}\DecValTok{3}\NormalTok{]}
\NormalTok{p\_solo\_bien1 }\OtherTok{\textless{}{-}}\NormalTok{ porcentajes[}\DecValTok{4}\NormalTok{]}
\NormalTok{p\_bien1\_bien2 }\OtherTok{\textless{}{-}}\NormalTok{ porcentajes[}\DecValTok{5}\NormalTok{]}

\CommentTok{\# Asegurar que los porcentajes suman exactamente 100\%}
\FunctionTok{test\_that}\NormalTok{(}\StringTok{"Los porcentajes ajustados suman 100\%"}\NormalTok{, \{}
  \FunctionTok{expect\_equal}\NormalTok{(}\FunctionTok{sum}\NormalTok{(porcentajes), }\DecValTok{100}\NormalTok{)}
\NormalTok{\})}
\end{Highlighting}
\end{Shaded}

Test passed

\begin{Shaded}
\begin{Highlighting}[]
\CommentTok{\# Aleatorizar el tipo de pregunta y la condición inicial}
\NormalTok{tipos\_pregunta }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(}
  \StringTok{"solo\_bien1"}\NormalTok{,}
  \StringTok{"solo\_bien2"}\NormalTok{,}
  \StringTok{"solo\_bien3"}\NormalTok{,}
  \StringTok{"bien1\_bien2"}\NormalTok{,}
  \StringTok{"bien1\_bien3"}
\NormalTok{)}

\CommentTok{\# Aleatorizar la condición inicial (dato conocido)}
\NormalTok{tipos\_condicion }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(}
  \StringTok{"bien1\_bien3"}\NormalTok{,}
  \StringTok{"solo\_bien3"}\NormalTok{,}
  \StringTok{"solo\_bien2"}\NormalTok{,}
  \StringTok{"solo\_bien1"}\NormalTok{,}
  \StringTok{"bien1\_bien2"}
\NormalTok{)}

\CommentTok{\# Seleccionar aleatoriamente el tipo de pregunta y condición}
\NormalTok{tipo\_pregunta }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(tipos\_pregunta, }\DecValTok{1}\NormalTok{)}
\NormalTok{tipo\_condicion }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(tipos\_condicion, }\DecValTok{1}\NormalTok{)}

\CommentTok{\# Determinar la respuesta correcta según el tipo de pregunta}
\ControlFlowTok{if}\NormalTok{ (tipo\_pregunta }\SpecialCharTok{==} \StringTok{"solo\_bien1"}\NormalTok{) \{}
\NormalTok{  respuesta\_correcta }\OtherTok{\textless{}{-}}\NormalTok{ personas\_solo\_bien1}
\NormalTok{  texto\_pregunta }\OtherTok{\textless{}{-}} \FunctionTok{paste0}\NormalTok{(}\StringTok{"solo "}\NormalTok{, bien1)}
\NormalTok{\} }\ControlFlowTok{else} \ControlFlowTok{if}\NormalTok{ (tipo\_pregunta }\SpecialCharTok{==} \StringTok{"solo\_bien2"}\NormalTok{) \{}
\NormalTok{  respuesta\_correcta }\OtherTok{\textless{}{-}}\NormalTok{ personas\_solo\_bien2}
\NormalTok{  texto\_pregunta }\OtherTok{\textless{}{-}} \FunctionTok{paste0}\NormalTok{(}\StringTok{"solo "}\NormalTok{, bien2)}
\NormalTok{\} }\ControlFlowTok{else} \ControlFlowTok{if}\NormalTok{ (tipo\_pregunta }\SpecialCharTok{==} \StringTok{"solo\_bien3"}\NormalTok{) \{}
\NormalTok{  respuesta\_correcta }\OtherTok{\textless{}{-}}\NormalTok{ personas\_solo\_bien3}
\NormalTok{  texto\_pregunta }\OtherTok{\textless{}{-}} \FunctionTok{paste0}\NormalTok{(}\StringTok{"solo "}\NormalTok{, bien3)}
\NormalTok{\} }\ControlFlowTok{else} \ControlFlowTok{if}\NormalTok{ (tipo\_pregunta }\SpecialCharTok{==} \StringTok{"bien1\_bien2"}\NormalTok{) \{}
\NormalTok{  respuesta\_correcta }\OtherTok{\textless{}{-}}\NormalTok{ personas\_bien1\_bien2}
\NormalTok{  texto\_pregunta }\OtherTok{\textless{}{-}} \FunctionTok{paste0}\NormalTok{(bien1, }\StringTok{" y "}\NormalTok{, bien2)}
\NormalTok{\} }\ControlFlowTok{else} \ControlFlowTok{if}\NormalTok{ (tipo\_pregunta }\SpecialCharTok{==} \StringTok{"bien1\_bien3"}\NormalTok{) \{}
\NormalTok{  respuesta\_correcta }\OtherTok{\textless{}{-}}\NormalTok{ personas\_bien1\_bien3}
\NormalTok{  texto\_pregunta }\OtherTok{\textless{}{-}} \FunctionTok{paste0}\NormalTok{(bien1, }\StringTok{" y "}\NormalTok{, bien3)}
\NormalTok{\}}

\CommentTok{\# Determinar el valor y texto de la condición inicial}
\ControlFlowTok{if}\NormalTok{ (tipo\_condicion }\SpecialCharTok{==} \StringTok{"bien1\_bien3"}\NormalTok{) \{}
\NormalTok{  valor\_condicion }\OtherTok{\textless{}{-}}\NormalTok{ personas\_bien1\_bien3}
\NormalTok{  texto\_condicion }\OtherTok{\textless{}{-}} \FunctionTok{paste0}\NormalTok{(bien1, }\StringTok{" y "}\NormalTok{, bien3)}
\NormalTok{\} }\ControlFlowTok{else} \ControlFlowTok{if}\NormalTok{ (tipo\_condicion }\SpecialCharTok{==} \StringTok{"solo\_bien3"}\NormalTok{) \{}
\NormalTok{  valor\_condicion }\OtherTok{\textless{}{-}}\NormalTok{ personas\_solo\_bien3}
\NormalTok{  texto\_condicion }\OtherTok{\textless{}{-}} \FunctionTok{paste0}\NormalTok{(}\StringTok{"solo "}\NormalTok{, bien3)}
\NormalTok{\} }\ControlFlowTok{else} \ControlFlowTok{if}\NormalTok{ (tipo\_condicion }\SpecialCharTok{==} \StringTok{"solo\_bien2"}\NormalTok{) \{}
\NormalTok{  valor\_condicion }\OtherTok{\textless{}{-}}\NormalTok{ personas\_solo\_bien2}
\NormalTok{  texto\_condicion }\OtherTok{\textless{}{-}} \FunctionTok{paste0}\NormalTok{(}\StringTok{"solo "}\NormalTok{, bien2)}
\NormalTok{\} }\ControlFlowTok{else} \ControlFlowTok{if}\NormalTok{ (tipo\_condicion }\SpecialCharTok{==} \StringTok{"solo\_bien1"}\NormalTok{) \{}
\NormalTok{  valor\_condicion }\OtherTok{\textless{}{-}}\NormalTok{ personas\_solo\_bien1}
\NormalTok{  texto\_condicion }\OtherTok{\textless{}{-}} \FunctionTok{paste0}\NormalTok{(}\StringTok{"solo "}\NormalTok{, bien1)}
\NormalTok{\} }\ControlFlowTok{else} \ControlFlowTok{if}\NormalTok{ (tipo\_condicion }\SpecialCharTok{==} \StringTok{"bien1\_bien2"}\NormalTok{) \{}
\NormalTok{  valor\_condicion }\OtherTok{\textless{}{-}}\NormalTok{ personas\_bien1\_bien2}
\NormalTok{  texto\_condicion }\OtherTok{\textless{}{-}} \FunctionTok{paste0}\NormalTok{(bien1, }\StringTok{" y "}\NormalTok{, bien2)}
\NormalTok{\}}

\CommentTok{\# Generar distractores plausibles según el tipo de pregunta}
\CommentTok{\# Usamos diferentes valores para crear distractores convincentes}
\NormalTok{distractor1 }\OtherTok{\textless{}{-}} \FunctionTok{round}\NormalTok{(respuesta\_correcta }\SpecialCharTok{*} \FunctionTok{sample}\NormalTok{(}\FunctionTok{c}\NormalTok{(}\FloatTok{0.7}\NormalTok{, }\FloatTok{0.8}\NormalTok{, }\FloatTok{1.2}\NormalTok{, }\FloatTok{1.3}\NormalTok{), }\DecValTok{1}\NormalTok{))  }\CommentTok{\# Variación porcentual}
\NormalTok{distractor2 }\OtherTok{\textless{}{-}} \FunctionTok{round}\NormalTok{(total\_personas }\SpecialCharTok{*} \FunctionTok{sample}\NormalTok{(}\FunctionTok{c}\NormalTok{(}\FloatTok{0.15}\NormalTok{, }\FloatTok{0.2}\NormalTok{, }\FloatTok{0.25}\NormalTok{, }\FloatTok{0.3}\NormalTok{), }\DecValTok{1}\NormalTok{))    }\CommentTok{\# Porcentaje arbitrario del total}
\NormalTok{distractor3 }\OtherTok{\textless{}{-}}\NormalTok{ valor\_condicion  }\CommentTok{\# El valor dado en la condición inicial}

\CommentTok{\# Asegurarse de que todos los distractores son diferentes de la respuesta correcta}
\ControlFlowTok{if}\NormalTok{ (distractor1 }\SpecialCharTok{==}\NormalTok{ respuesta\_correcta) distractor1 }\OtherTok{\textless{}{-}}\NormalTok{ distractor1 }\SpecialCharTok{+} \FunctionTok{sample}\NormalTok{(}\DecValTok{5}\SpecialCharTok{:}\DecValTok{15}\NormalTok{, }\DecValTok{1}\NormalTok{)}
\ControlFlowTok{if}\NormalTok{ (distractor2 }\SpecialCharTok{==}\NormalTok{ respuesta\_correcta) distractor2 }\OtherTok{\textless{}{-}}\NormalTok{ distractor2 }\SpecialCharTok{{-}} \FunctionTok{sample}\NormalTok{(}\DecValTok{5}\SpecialCharTok{:}\DecValTok{15}\NormalTok{, }\DecValTok{1}\NormalTok{)}
\ControlFlowTok{if}\NormalTok{ (distractor3 }\SpecialCharTok{==}\NormalTok{ respuesta\_correcta) distractor3 }\OtherTok{\textless{}{-}}\NormalTok{ distractor3 }\SpecialCharTok{+} \FunctionTok{sample}\NormalTok{(}\DecValTok{5}\SpecialCharTok{:}\DecValTok{15}\NormalTok{, }\DecValTok{1}\NormalTok{)}

\CommentTok{\# Asegurarse de que todos los distractores son diferentes entre sí}
\ControlFlowTok{while}\NormalTok{ (}\FunctionTok{length}\NormalTok{(}\FunctionTok{unique}\NormalTok{(}\FunctionTok{c}\NormalTok{(distractor1, distractor2, distractor3))) }\SpecialCharTok{\textless{}} \DecValTok{3}\NormalTok{) \{}
  \ControlFlowTok{if}\NormalTok{ (distractor1 }\SpecialCharTok{==}\NormalTok{ distractor2) distractor1 }\OtherTok{\textless{}{-}}\NormalTok{ distractor1 }\SpecialCharTok{+} \FunctionTok{sample}\NormalTok{(}\DecValTok{5}\SpecialCharTok{:}\DecValTok{10}\NormalTok{, }\DecValTok{1}\NormalTok{)}
  \ControlFlowTok{if}\NormalTok{ (distractor2 }\SpecialCharTok{==}\NormalTok{ distractor3) distractor2 }\OtherTok{\textless{}{-}}\NormalTok{ distractor2 }\SpecialCharTok{{-}} \FunctionTok{sample}\NormalTok{(}\DecValTok{5}\SpecialCharTok{:}\DecValTok{10}\NormalTok{, }\DecValTok{1}\NormalTok{)}
  \ControlFlowTok{if}\NormalTok{ (distractor1 }\SpecialCharTok{==}\NormalTok{ distractor3) distractor3 }\OtherTok{\textless{}{-}}\NormalTok{ distractor3 }\SpecialCharTok{+} \FunctionTok{sample}\NormalTok{(}\DecValTok{5}\SpecialCharTok{:}\DecValTok{10}\NormalTok{, }\DecValTok{1}\NormalTok{)}
\NormalTok{\}}

\CommentTok{\# Crear un vector con todas las opciones y mezclarlas}
\NormalTok{opciones }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(respuesta\_correcta, distractor1, distractor2, distractor3)}
\FunctionTok{names}\NormalTok{(opciones) }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(}\StringTok{"correcta"}\NormalTok{, }\StringTok{"distractor1"}\NormalTok{, }\StringTok{"distractor2"}\NormalTok{, }\StringTok{"distractor3"}\NormalTok{)}
\NormalTok{opciones\_mezcladas }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(opciones)}

\CommentTok{\# Identificar la posición de la respuesta correcta en las opciones mezcladas}
\NormalTok{indice\_correcto }\OtherTok{\textless{}{-}} \FunctionTok{which}\NormalTok{(opciones\_mezcladas }\SpecialCharTok{==}\NormalTok{ respuesta\_correcta)}

\CommentTok{\# Crear el vector de solución para r{-}exams (primera pregunta)}
\NormalTok{solucion1 }\OtherTok{\textless{}{-}} \FunctionTok{rep}\NormalTok{(}\DecValTok{0}\NormalTok{, }\DecValTok{4}\NormalTok{)}
\NormalTok{solucion1[indice\_correcto] }\OtherTok{\textless{}{-}} \DecValTok{1}

\CommentTok{\# Configuración para la segunda pregunta (probabilidad)}
\CommentTok{\# Calcular la probabilidad correcta de que una persona tenga solo carro}
\NormalTok{prob\_solo\_carro }\OtherTok{\textless{}{-}}\NormalTok{ personas\_solo\_bien1 }\SpecialCharTok{/}\NormalTok{ total\_personas}

\CommentTok{\# Opciones de respuesta para la probabilidad (según la imagen)}
\NormalTok{opciones\_prob }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(}\StringTok{"1/10"}\NormalTok{, }\StringTok{"3/50"}\NormalTok{, }\StringTok{"1/3"}\NormalTok{, }\StringTok{"1/5"}\NormalTok{)}

\CommentTok{\# Determinar cuál es la respuesta correcta entre las opciones dadas}
\CommentTok{\# Convertir las fracciones a valores decimales para comparar}
\NormalTok{opciones\_prob\_decimal }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(}\DecValTok{1}\SpecialCharTok{/}\DecValTok{10}\NormalTok{, }\DecValTok{3}\SpecialCharTok{/}\DecValTok{50}\NormalTok{, }\DecValTok{1}\SpecialCharTok{/}\DecValTok{3}\NormalTok{, }\DecValTok{1}\SpecialCharTok{/}\DecValTok{5}\NormalTok{)}
\NormalTok{diferencias }\OtherTok{\textless{}{-}} \FunctionTok{abs}\NormalTok{(opciones\_prob\_decimal }\SpecialCharTok{{-}}\NormalTok{ prob\_solo\_carro)}
\NormalTok{indice\_correcto\_prob }\OtherTok{\textless{}{-}} \FunctionTok{which.min}\NormalTok{(diferencias)}

\CommentTok{\# Crear el vector de solución para la segunda pregunta}
\NormalTok{solucion2 }\OtherTok{\textless{}{-}} \FunctionTok{rep}\NormalTok{(}\DecValTok{0}\NormalTok{, }\DecValTok{4}\NormalTok{)}
\NormalTok{solucion2[indice\_correcto\_prob] }\OtherTok{\textless{}{-}} \DecValTok{1}
\end{Highlighting}
\end{Shaded}

\begin{Shaded}
\begin{Highlighting}[]
\FunctionTok{options}\NormalTok{(}\AttributeTok{OutDec =} \StringTok{"."}\NormalTok{)  }\CommentTok{\# Asegurar punto decimal en este chunk}

\CommentTok{\# Código Python para generar el gráfico circular}
\NormalTok{codigo\_python }\OtherTok{\textless{}{-}} \FunctionTok{paste0}\NormalTok{(}\StringTok{"}
\StringTok{import matplotlib}
\StringTok{matplotlib.use(\textquotesingle{}Agg\textquotesingle{})  \# Usar backend no interactivo}
\StringTok{import matplotlib.pyplot as plt}
\StringTok{import numpy as np}

\StringTok{\# Datos para el gráfico}
\StringTok{labels = [\textquotesingle{}"}\NormalTok{, bien1, }\StringTok{" y "}\NormalTok{, bien3, }\StringTok{"\textquotesingle{}, \textquotesingle{}Solo "}\NormalTok{, bien3, }\StringTok{"\textquotesingle{}, \textquotesingle{}Solo "}\NormalTok{, bien2, }\StringTok{"\textquotesingle{},}
\StringTok{          \textquotesingle{}Solo "}\NormalTok{, bien1, }\StringTok{"\textquotesingle{}, \textquotesingle{}"}\NormalTok{, bien1, }\StringTok{" y "}\NormalTok{, bien2, }\StringTok{"\textquotesingle{}]}
\StringTok{sizes = ["}\NormalTok{, p\_bien1\_bien3, }\StringTok{", "}\NormalTok{, p\_solo\_bien3, }\StringTok{", "}\NormalTok{, p\_solo\_bien2, }\StringTok{",}
\StringTok{         "}\NormalTok{, p\_solo\_bien1, }\StringTok{", "}\NormalTok{, p\_bien1\_bien2, }\StringTok{"]}
\StringTok{colors = [\textquotesingle{}"}\NormalTok{, paleta\_seleccionada[}\DecValTok{1}\NormalTok{], }\StringTok{"\textquotesingle{}, \textquotesingle{}"}\NormalTok{, paleta\_seleccionada[}\DecValTok{2}\NormalTok{], }\StringTok{"\textquotesingle{},}
\StringTok{          \textquotesingle{}"}\NormalTok{, paleta\_seleccionada[}\DecValTok{3}\NormalTok{], }\StringTok{"\textquotesingle{}, \textquotesingle{}"}\NormalTok{, paleta\_seleccionada[}\DecValTok{4}\NormalTok{], }\StringTok{"\textquotesingle{},}
\StringTok{          \textquotesingle{}"}\NormalTok{, paleta\_seleccionada[}\DecValTok{5}\NormalTok{], }\StringTok{"\textquotesingle{}]}

\StringTok{\# Explode para destacar ligeramente el sector con bien1 y bien3}
\StringTok{explode = (0.03, 0, 0, 0, 0)}

\StringTok{\# Crear figura con más espacio para acomodar las etiquetas externas}
\StringTok{plt.figure(figsize=(7, 6))  \# Aumentar tamaño para dar espacio a las etiquetas}

\StringTok{\# Crear gráfico circular con bordes blancos}
\StringTok{wedges, texts, autotexts = plt.pie(}
\StringTok{    sizes,}
\StringTok{    explode=explode,}
\StringTok{    colors=colors,}
\StringTok{    shadow=True,}
\StringTok{    startangle=45,  \# Cambiar ángulo para mejor distribución}
\StringTok{    autopct=\textquotesingle{}\%d\%\%\textquotesingle{},}
\StringTok{    pctdistance=0.75,  \# Ubicar porcentajes más cerca del borde exterior}
\StringTok{    wedgeprops=\{\textquotesingle{}edgecolor\textquotesingle{}: \textquotesingle{}white\textquotesingle{}, \textquotesingle{}linewidth\textquotesingle{}: 1\},}
\StringTok{    textprops=\{\textquotesingle{}fontsize\textquotesingle{}: 10, \textquotesingle{}fontweight\textquotesingle{}: \textquotesingle{}bold\textquotesingle{}, \textquotesingle{}color\textquotesingle{}: \textquotesingle{}white\textquotesingle{}\}}
\StringTok{)}

\StringTok{\# Configuración estética de los textos de porcentaje con recuadros}
\StringTok{for autotext in autotexts:}
\StringTok{    autotext.set\_fontsize(9)  \# Tamaño de fuente para porcentajes}
\StringTok{    autotext.set\_weight(\textquotesingle{}bold\textquotesingle{})}
\StringTok{    \# Crear un recuadro semitransparente para los porcentajes}
\StringTok{    bbox\_props = \{\textquotesingle{}boxstyle\textquotesingle{}: \textquotesingle{}round,pad=0.2\textquotesingle{},}
\StringTok{                 \textquotesingle{}facecolor\textquotesingle{}: \textquotesingle{}black\textquotesingle{},}
\StringTok{                 \textquotesingle{}alpha\textquotesingle{}: 0.7,}
\StringTok{                 \textquotesingle{}edgecolor\textquotesingle{}: \textquotesingle{}none\textquotesingle{}\}}
\StringTok{    autotext.set\_bbox(bbox\_props)}

\StringTok{\# Eliminar los textos del pie (los reemplazaremos con etiquetas externas)}
\StringTok{for text in texts:}
\StringTok{    text.set\_visible(False)}

\StringTok{\# Crear etiquetas externas con líneas conectoras}
\StringTok{bbox\_props = \{\textquotesingle{}boxstyle\textquotesingle{}: \textquotesingle{}round,pad=0.3\textquotesingle{},}
\StringTok{             \textquotesingle{}facecolor\textquotesingle{}: \textquotesingle{}white\textquotesingle{},}
\StringTok{             \textquotesingle{}edgecolor\textquotesingle{}: \textquotesingle{}gray\textquotesingle{},}
\StringTok{             \textquotesingle{}alpha\textquotesingle{}: 0.9\}}

\StringTok{\# Calcular posiciones de etiquetas externas optimizadas para evitar solapamiento}
\StringTok{def get\_label\_position(angle\_rad, wedge\_size):}
\StringTok{    \# Ajustar distancia basada en el tamaño del sector}
\StringTok{    \# Sectores más pequeños tienen etiquetas más alejadas para evitar solapamiento}
\StringTok{    distance\_factor = 1.25 if wedge\_size \textless{} 15 else 1.15}

\StringTok{    \# Determinar coordenadas}
\StringTok{    x = distance\_factor * np.cos(angle\_rad)}
\StringTok{    y = distance\_factor * np.sin(angle\_rad)}

\StringTok{    \# Ajustar alineación basada en cuadrante}
\StringTok{    if x \textless{} 0:}
\StringTok{        ha = \textquotesingle{}right\textquotesingle{}}
\StringTok{    else:}
\StringTok{        ha = \textquotesingle{}left\textquotesingle{}}

\StringTok{    if y \textless{} 0:}
\StringTok{        va = \textquotesingle{}top\textquotesingle{}}
\StringTok{    else:}
\StringTok{        va = \textquotesingle{}bottom\textquotesingle{}}

\StringTok{    \# Para sectores muy pequeños, ajustar más la posición para evitar solapamiento}
\StringTok{    if wedge\_size \textless{} 10:}
\StringTok{        x *= 1.1}
\StringTok{        y *= 1.1}

\StringTok{    return x, y, ha, va}

\StringTok{\# Colocar etiquetas externas con líneas conectoras}
\StringTok{for i, wedge in enumerate(wedges):}
\StringTok{    \# Calcular ángulo medio del sector}
\StringTok{    ang = (wedge.theta1 + wedge.theta2) / 2}
\StringTok{    ang\_rad = np.radians(ang)}

\StringTok{    \# Calcular coordenadas para el inicio de la línea conectora (en el borde del sector)}
\StringTok{    \# El factor 0.85 asegura que la línea empiece cerca del borde del sector}
\StringTok{    x\_edge = 0.85 * np.cos(ang\_rad)}
\StringTok{    y\_edge = 0.85 * np.sin(ang\_rad)}

\StringTok{    \# Obtener posición optimizada para la etiqueta}
\StringTok{    x\_label, y\_label, ha, va = get\_label\_position(ang\_rad, sizes[i])}

\StringTok{    \# Dibujar línea conectora}
\StringTok{    con = plt.annotate(\textquotesingle{}\textquotesingle{},}
\StringTok{                      xy=(x\_edge, y\_edge),  \# Inicio (en el borde del sector)}
\StringTok{                      xytext=(x\_label * 0.95, y\_label * 0.95),  \# Fin (cerca de la etiqueta)}
\StringTok{                      arrowprops=dict(arrowstyle=\textquotesingle{}{-}\textquotesingle{}, color=\textquotesingle{}gray\textquotesingle{}, lw=0.8))}

\StringTok{    \# Añadir etiqueta con recuadro blanco}
\StringTok{    plt.text(x\_label, y\_label, labels[i],}
\StringTok{            fontsize=8,}
\StringTok{            fontweight=\textquotesingle{}bold\textquotesingle{},}
\StringTok{            ha=ha,}
\StringTok{            va=va,}
\StringTok{            bbox=bbox\_props,}
\StringTok{            zorder=10)}

\StringTok{\# Asegurar que el gráfico sea un círculo}
\StringTok{plt.axis(\textquotesingle{}equal\textquotesingle{})}

\StringTok{\# Añadir título en la parte inferior del gráfico}
\StringTok{plt.figtext(0.5, 0.01,  \# Posición x=0.5 (centro), y=0.01 (parte inferior)}
\StringTok{           \textquotesingle{}Distribución de bienes\textquotesingle{},}
\StringTok{           fontsize=12,}
\StringTok{           fontweight=\textquotesingle{}bold\textquotesingle{},}
\StringTok{           color=\textquotesingle{}\#333333\textquotesingle{},}
\StringTok{           ha=\textquotesingle{}center\textquotesingle{})  \# Alineación horizontal centrada}

\StringTok{\# Ajustar los márgenes para dejar espacio a las etiquetas externas}
\StringTok{plt.tight\_layout(pad=1.5, rect=[0, 0.05, 1, 0.95])  \# Ajustar rect para dar más espacio}

\StringTok{\# Guardar en múltiples formatos para asegurar compatibilidad}
\StringTok{plt.savefig(\textquotesingle{}grafico\_circular.png\textquotesingle{}, dpi=150, bbox\_inches=\textquotesingle{}tight\textquotesingle{},}
\StringTok{           transparent=True, format=\textquotesingle{}png\textquotesingle{})}
\StringTok{plt.savefig(\textquotesingle{}grafico\_circular.pdf\textquotesingle{}, dpi=150, bbox\_inches=\textquotesingle{}tight\textquotesingle{},}
\StringTok{           transparent=True, format=\textquotesingle{}pdf\textquotesingle{})}
\StringTok{plt.close()}
\StringTok{"}\NormalTok{)}

\CommentTok{\# Ejecutar código Python para generar la figura}
\FunctionTok{py\_run\_string}\NormalTok{(codigo\_python)}
\end{Highlighting}
\end{Shaded}

\section{Question}\label{question}

En La Tebaida se realizó un(a) investigación a un grupo de trabajadores
de un(a) organización sobre el tipo de posesiones que poseen. Los(las)
estadísticas se presentan en la gráfica.

\includegraphics[width=0.7\linewidth,height=\textheight,keepaspectratio]{grafico_circular.png}

\#\#ANSWER1\#\#

Si 256 trabajadores de el(la) organización poseen solo casa, ¿cuántas
personas poseen solo casa?

\subsection{Answerlist}\label{answerlist}

\begin{itemize}
\tightlist
\item
  307
\item
  200
\item
  256
\item
  267
\end{itemize}

\#\#ANSWER2\#\#

Si se escoge una persona del grupo al azar, ¿cuál es la probabilidad de
que tenga solo auto?

\subsection{Answerlist}\label{answerlist-1}

\begin{itemize}
\tightlist
\item
  1/10
\item
  3/50
\item
  1/3
\item
  1/5
\end{itemize}

\section{Solution}\label{solution}

\#\#ANSWER1\#\#

Para resolver la primera pregunta, necesitamos aplicar proporciones y
regla de tres a partir de la información dada en el gráfico circular y
el enunciado. Seguiremos un proceso paso a paso:

\subsubsection{Paso 1: Identificar los datos
conocidos:}\label{paso-1-identificar-los-datos-conocidos}

\begin{itemize}
\tightlist
\item
  Sabemos que 256 trabajadores poseen solo casa.
\item
  Según el gráfico circular, este grupo representa el 32\% del total.
\item
  También observamos en el gráfico que las personas que poseen solo casa
  representan el 32\% del total.
\end{itemize}

\subsubsection{Paso 2: Calcular el número total de
personas:}\label{paso-2-calcular-el-nuxfamero-total-de-personas}

Para encontrar el total de trabajadores en la organización, utilizamos
la siguiente relación de proporcionalidad:

Si 32\% del total = 256 trabajadores.

Entonces 100\% del total = X trabajadores

Aplicando regla de tres:

\begin{itemize}
\tightlist
\item
  X = (256 × 100\%) ÷ 32\%
\item
  X = 25600 ÷ 32
\item
  X = 800 trabajadores
\end{itemize}

Por lo tanto, el total de trabajadores en la organización es 800.

\subsubsection{Paso 3: Calcular el número de personas que poseen solo
casa}\label{paso-3-calcular-el-nuxfamero-de-personas-que-poseen-solo-casa}

Una vez conocido el total, podemos calcular cuántas personas poseen solo
casa utilizando el porcentaje correspondiente del gráfico circular:

Si 100\% del total = 800 trabajadores.

Entonces 32\% del total = Y trabajadores

Aplicando regla de tres:

\begin{itemize}
\tightlist
\item
  Y = (32\% × 800) ÷ 100\%
\item
  Y = (32 × 800) ÷ 100
\item
  Y = 256
\item
  Y = 256 trabajadores
\end{itemize}

\subsubsection{Conclusión}\label{conclusiuxf3n}

Por lo tanto, 256 trabajadores de la organización poseen solo casa.

\subsection{Answerlist}\label{answerlist-2}

\begin{itemize}
\tightlist
\item
  Falso
\item
  Falso
\item
  Verdadero
\item
  Falso
\end{itemize}

\#\#ANSWER2\#\#

Para resolver la segunda pregunta, necesitamos calcular la probabilidad
de que una persona escogida al azar tenga solo auto.

\subsubsection{Paso 1: Identificar los datos
relevantes}\label{paso-1-identificar-los-datos-relevantes}

Del gráfico circular y los cálculos anteriores, sabemos que: * El número
total de personas es 800 * El número de personas que poseen solo auto es
80 * El porcentaje de personas que poseen solo auto es 10\%

\subsubsection{Paso 2: Calcular la
probabilidad}\label{paso-2-calcular-la-probabilidad}

La probabilidad de un evento se calcula como:

Probabilidad = Número de casos favorables / Número de casos posibles

En este caso: * Casos favorables: Personas que poseen solo auto = 80 *
Casos posibles: Total de personas = 800

Por lo tanto: * Probabilidad = 80 / 800 = 0.1

\subsubsection{Paso 3: Expresar la probabilidad como
fracción}\label{paso-3-expresar-la-probabilidad-como-fracciuxf3n}

La probabilidad 0.1 expresada como fracción simplificada es
aproximadamente 1/10.

\subsubsection{Verificación}\label{verificaciuxf3n}

Podemos verificar que esta es la opción más cercana a la probabilidad
real: * Probabilidad real: 80 / 800 = 0.1 * Opciones disponibles: - 1/10
= 0.1 - 3/50 = 0.06 - 1/3 = 0.333\ldots{} - 1/5 = 0.2

La opción más cercana a 0.1 es 1/10.

\subsection{Answerlist}\label{answerlist-3}

\begin{itemize}
\tightlist
\item
  Verdadero
\item
  Falso
\item
  Falso
\item
  Falso
\end{itemize}

\section{Meta-information}\label{meta-information}

exname: proporciones\_diagrama\_circular extype: cloze exclozetype:
schoice\textbar schoice exsolution: 0010\textbar1000 exshuffle:
TRUE\textbar TRUE exsection:
Estadística\textbar Proporciones\textbar Interpretación de gráficos

\end{document}
