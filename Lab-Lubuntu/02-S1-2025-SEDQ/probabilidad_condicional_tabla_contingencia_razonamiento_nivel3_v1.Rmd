
---
output:
  word_document: default
  html_document: default
  pdf_document:
    keep_tex: true
    extra_dependencies: ["graphicx", "float"]
---

```{r setup, include=FALSE}
# Configuración para todos los formatos de salida
Sys.setlocale(category = "LC_NUMERIC", locale = "C")
options(OutDec = ".")

# Configurar el motor LaTeX globalmente
options(tikzLatex = "pdflatex")
options(tikzXelatex = FALSE)
options(tikzLatexPackages = c(
  "\\usepackage{tikz}",
  "\\usepackage{colortbl}",
  "\\usepackage{graphicx}",
  "\\usepackage{float}"
))

library(exams)
library(reticulate)
library(digest)
library(testthat)
library(knitr)

typ <- match_exams_device()
options(scipen = 999)
knitr::opts_chunk$set(
  warning = FALSE,
  message = FALSE,
  fig.showtext = FALSE,
  fig.cap = "",
  fig.keep = 'all',
  dev = c("png", "pdf"),
  dpi = 150,
  fig.pos = "H"
)
```

```{r DefinicionDeVariables, include=FALSE}
options(OutDec = ".")  # Asegurar punto decimal en este chunk

# Establecer semilla aleatoria
set.seed(sample(1:10000, 1))

# ALEATORIZACIÓN AVANZADA DEL CONTEXTO DEL PROBLEMA (15 opciones)
contextos_educativos <- c(
  "taller de verano", "curso vacacional", "seminario intensivo", "programa de capacitación",
  "curso de extensión", "diplomado especializado", "workshop educativo", 
  "entrenamiento técnico", "curso intensivo", "programa formativo",
  "encuentro formativo", "programa de desarrollo"
)
contexto_seleccionado <- sample(contextos_educativos, 1)

# ALEATORIZACIÓN DE LA EDAD DE CORTE (8 opciones)
edades_corte_disponibles <- c(16, 17, 18, 19, 20, 21, 22, 25)
edad_corte_seleccionada <- sample(edades_corte_disponibles, 1)

# ALEATORIZACIÓN DE TÉRMINOS PARA GÉNEROS (6 + 5 opciones)
terminos_masculino_disponibles <- c("hombres", "varones", "participantes masculinos",
                                   "estudiantes masculinos", "asistentes masculinos", "miembros masculinos")
terminos_femenino_disponibles <- c("mujeres", "participantes de género femenino", "estudiantes de género femenino",
                                   "personas de género femenino")
termino_masculino_seleccionado <- sample(terminos_masculino_disponibles, 1)
termino_femenino_seleccionado <- sample(terminos_femenino_disponibles, 1)

# ALEATORIZACIÓN DE TÉRMINOS PARA GRUPOS DE EDAD (4 + 4 opciones)
terminos_menores_disponibles <- c("menores")
terminos_mayores_disponibles <- c("mayores")
termino_menores_seleccionado <- sample(terminos_menores_disponibles, 1)
termino_mayores_seleccionado <- sample(terminos_mayores_disponibles, 1)

# ALEATORIZACIÓN DE TÉRMINOS GENERALES (7 + 6 + 5 opciones)
terminos_participantes_disponibles <- c("participantes", "estudiantes", "asistentes",
                                       "asistentes del grupo", "miembros", "integrantes", "asistentes al programa")
termino_participantes_seleccionado <- sample(terminos_participantes_disponibles, 1)

terminos_tabla_disponibles <- c("cuadro", "esquema", "diagrama")
termino_tabla_seleccionado <- sample(terminos_tabla_disponibles, 1)

terminos_proporciones_disponibles <- c("datos estadísticos","valores proporcionales")
termino_proporciones_seleccionado <- sample(terminos_proporciones_disponibles, 1)

# FUNCIÓN CORREGIDA PARA GENERAR PROPORCIONES MATEMÁTICAMENTE COHERENTES
generar_proporciones_coherentes <- function() {
  repeat {
    # Generar valores base con distribuciones realistas
    tipo_distribucion <- sample(1:5, 1)
    
    if (tipo_distribucion == 1) {
      # Distribución equilibrada
      p11_base <- sample(18:28, 1)   # Menores masculino
      p12_base <- sample(15:25, 1)   # Menores femenino  
      p21_base <- sample(25:35, 1)   # Mayores masculino
      p22_base <- sample(20:30, 1)   # Mayores femenino
    } else if (tipo_distribucion == 2) {
      # Predominio femenino
      p11_base <- sample(10:20, 1)   # Menores masculino (menor)
      p12_base <- sample(20:30, 1)   # Menores femenino (mayor)
      p21_base <- sample(15:25, 1)   # Mayores masculino (menor)
      p22_base <- sample(30:40, 1)   # Mayores femenino (mayor)
    } else if (tipo_distribucion == 3) {
      # Predominio masculino
      p11_base <- sample(20:30, 1)   # Menores masculino (mayor)
      p12_base <- sample(10:20, 1)   # Menores femenino (menor)
      p21_base <- sample(30:40, 1)   # Mayores masculino (mayor)
      p22_base <- sample(15:25, 1)   # Mayores femenino (menor)
    } else if (tipo_distribucion == 4) {
      # Predominio de mayores
      p11_base <- sample(8:18, 1)    # Menores masculino (menor)
      p12_base <- sample(12:22, 1)   # Menores femenino (menor)
      p21_base <- sample(35:45, 1)   # Mayores masculino (mayor)
      p22_base <- sample(25:35, 1)   # Mayores femenino (mayor)
    } else {
      # Predominio de menores
      p11_base <- sample(25:35, 1)   # Menores masculino (mayor)
      p12_base <- sample(20:30, 1)   # Menores femenino (mayor)
      p21_base <- sample(15:25, 1)   # Mayores masculino (menor)
      p22_base <- sample(10:20, 1)   # Mayores femenino (menor)
    }
    
    # Normalización matemática exacta
    total_base <- p11_base + p12_base + p21_base + p22_base
    
    # Convertir a proporciones con 3 decimales para mayor precisión
    p11 <- round(p11_base / total_base, 3)
    p12 <- round(p12_base / total_base, 3)
    p21 <- round(p21_base / total_base, 3)
    p22 <- round(p22_base / total_base, 3)
    
    # Ajuste fino para asegurar suma exacta de 1.0
    suma_actual <- p11 + p12 + p21 + p22
    diferencia <- 1.0 - suma_actual
    
    # Distribuir la diferencia en el valor más grande
    valores <- c(p11, p12, p21, p22)
    indice_max <- which.max(valores)
    
    if (indice_max == 1) p11 <- p11 + diferencia
    else if (indice_max == 2) p12 <- p12 + diferencia
    else if (indice_max == 3) p21 <- p21 + diferencia
    else p22 <- p22 + diferencia
    
    # Redondear nuevamente para mantener precisión
    p11 <- round(p11, 3)
    p12 <- round(p12, 3)
    p21 <- round(p21, 3)
    p22 <- round(p22, 3)
    
    # VALIDACIÓN ESTRICTA DE COHERENCIA MATEMÁTICA
    suma_final <- p11 + p12 + p21 + p22
    
    # Verificar condiciones de validez
    if (abs(suma_final - 1.0) <= 0.001 &&  # Suma exacta
        p11 >= 0.05 && p11 <= 0.50 &&      # Rangos válidos
        p12 >= 0.05 && p12 <= 0.50 &&
        p21 >= 0.05 && p21 <= 0.60 &&
        p22 >= 0.05 && p22 <= 0.60 &&
        (p11 + p12) >= 0.20 &&              # Marginales válidas
        (p21 + p22) >= 0.30 &&
        (p11 + p21) >= 0.25 &&
        (p12 + p22) >= 0.25) {
      return(c(p11, p12, p21, p22))
    }
  }
}

# Obtener proporciones válidas y coherentes
proporciones_generadas <- generar_proporciones_coherentes()
p_menor_masc <- proporciones_generadas[1]  # P(Menor ∩ Masculino)
p_menor_fem <- proporciones_generadas[2]   # P(Menor ∩ Femenino)
p_mayor_masc <- proporciones_generadas[3]  # P(Mayor ∩ Masculino)
p_mayor_fem <- proporciones_generadas[4]   # P(Mayor ∩ Femenino)

# SCRIPTS DE PRUEBA DE INTEGRIDAD MATEMÁTICA ROBUSTOS
test_that("Suma de proporciones es exactamente 1.0", {
  suma_total <- sum(proporciones_generadas)
  expect_equal(suma_total, 1.0, tolerance = 0.001)
})

test_that("Todas las proporciones son positivas", {
  expect_true(all(proporciones_generadas > 0))
})

test_that("Ninguna proporción excede 1.0", {
  expect_true(all(proporciones_generadas < 1.0))
})

# Calcular probabilidades marginales
p_masculino <- p_menor_masc + p_mayor_masc
p_femenino <- p_menor_fem + p_mayor_fem
p_menor <- p_menor_masc + p_menor_fem
p_mayor <- p_mayor_masc + p_mayor_fem

test_that("Probabilidades marginales de género suman 1.0", {
  expect_equal(p_masculino + p_femenino, 1.0, tolerance = 0.001)
})

test_that("Probabilidades marginales de edad suman 1.0", {
  expect_equal(p_menor + p_mayor, 1.0, tolerance = 0.001)
})

# ALEATORIZACIÓN AVANZADA DE TIPOS DE PREGUNTA (8 tipos diferentes)
tipos_pregunta_disponibles <- list(
  list(condicion = "femenino", evento = "mayor",
       texto_condicion = termino_femenino_seleccionado,
       texto_evento = paste(termino_mayores_seleccionado, "de", edad_corte_seleccionada, "años"),
       numerador = p_mayor_fem, denominador = p_femenino),
  list(condicion = "masculino", evento = "mayor",
       texto_condicion = termino_masculino_seleccionado,
       texto_evento = paste(termino_mayores_seleccionado, "de", edad_corte_seleccionada, "años"),
       numerador = p_mayor_masc, denominador = p_masculino),
  list(condicion = "femenino", evento = "menor",
       texto_condicion = termino_femenino_seleccionado,
       texto_evento = paste(termino_menores_seleccionado, "de", edad_corte_seleccionada, "años"),
       numerador = p_menor_fem, denominador = p_femenino),
  list(condicion = "masculino", evento = "menor",
       texto_condicion = termino_masculino_seleccionado,
       texto_evento = paste(termino_menores_seleccionado, "de", edad_corte_seleccionada, "años"),
       numerador = p_menor_masc, denominador = p_masculino),
  list(condicion = "mayor", evento = "femenino",
       texto_condicion = paste(termino_mayores_seleccionado, "de", edad_corte_seleccionada, "años"),
       texto_evento = termino_femenino_seleccionado,
       numerador = p_mayor_fem, denominador = p_mayor),
  list(condicion = "mayor", evento = "masculino",
       texto_condicion = paste(termino_mayores_seleccionado, "de", edad_corte_seleccionada, "años"),
       texto_evento = termino_masculino_seleccionado,
       numerador = p_mayor_masc, denominador = p_mayor),
  list(condicion = "menor", evento = "femenino",
       texto_condicion = paste(termino_menores_seleccionado, "de", edad_corte_seleccionada, "años"),
       texto_evento = termino_femenino_seleccionado,
       numerador = p_menor_fem, denominador = p_menor),
  list(condicion = "menor", evento = "masculino",
       texto_condicion = paste(termino_menores_seleccionado, "de", edad_corte_seleccionada, "años"),
       texto_evento = termino_masculino_seleccionado,
       numerador = p_menor_masc, denominador = p_menor)
)

pregunta_seleccionada <- sample(tipos_pregunta_disponibles, 1)[[1]]

# Calcular la respuesta correcta con precisión estandarizada
respuesta_correcta_fraccion <- paste0(pregunta_seleccionada$numerador, "/", pregunta_seleccionada$denominador)
respuesta_correcta_decimal <- round(pregunta_seleccionada$numerador / pregunta_seleccionada$denominador, 3)

# FUNCIÓN MEJORADA PARA GENERAR DISTRACTORES DESAFIANTES Y ÚNICOS
generar_distractores_desafiantes <- function(numerador_correcto, denominador_correcto) {
  num_correcto <- as.numeric(numerador_correcto)
  den_correcto <- as.numeric(denominador_correcto)

  # Lista de estrategias para distractores desafiantes
  estrategias_distractores <- list()

  # ESTRATEGIA 1: Invertir P(A|B) por P(B|A) - Error conceptual muy común
  estrategias_distractores[[1]] <- paste0(denominador_correcto, "/", numerador_correcto)

  # ESTRATEGIA 2: Usar solo probabilidad conjunta (no dividir por marginal)
  estrategias_distractores[[2]] <- paste0(numerador_correcto, "/1.0")

  # ESTRATEGIA 3: Usar probabilidad marginal incorrecta en denominador
  if (den_correcto == p_masculino) {
    den_alternativo <- p_femenino
  } else if (den_correcto == p_femenino) {
    den_alternativo <- p_masculino
  } else if (den_correcto == p_mayor) {
    den_alternativo <- p_menor
  } else {
    den_alternativo <- p_mayor
  }
  estrategias_distractores[[3]] <- paste0(numerador_correcto, "/", den_alternativo)

  # ESTRATEGIA 4: Usar probabilidad conjunta incorrecta en numerador
  if (num_correcto == p_menor_masc) {
    num_alternativo <- p_mayor_masc
  } else if (num_correcto == p_menor_fem) {
    num_alternativo <- p_mayor_fem
  } else if (num_correcto == p_mayor_masc) {
    num_alternativo <- p_menor_masc
  } else {
    num_alternativo <- p_menor_fem
  }
  estrategias_distractores[[4]] <- paste0(num_alternativo, "/", denominador_correcto)

  # ESTRATEGIA 5: Usar complemento de la probabilidad
  estrategias_distractores[[5]] <- paste0(round(den_correcto - num_correcto, 3), "/", denominador_correcto)

  # ESTRATEGIA 6: Error de cálculo con factor multiplicativo
  factor_error <- sample(c(0.5, 0.75, 1.25, 1.5), 1)
  estrategias_distractores[[6]] <- paste0(round(num_correcto * factor_error, 3), "/", denominador_correcto)

  # Filtrar distractores válidos (diferentes de la respuesta correcta)
  distractores_validos <- c()
  for (distractor in estrategias_distractores) {
    if (distractor != respuesta_correcta_fraccion &&
        !distractor %in% distractores_validos &&
        !grepl("^0/", distractor) &&  # Evitar numeradores cero
        !grepl("/0", distractor)) {   # Evitar denominadores cero
      distractores_validos <- c(distractores_validos, distractor)
    }
  }

  # Asegurar que tenemos exactamente 3 distractores únicos
  if (length(distractores_validos) >= 3) {
    return(distractores_validos[1:3])
  } else {
    # Generar distractores adicionales si es necesario
    while (length(distractores_validos) < 3) {
      factor_aleatorio <- sample(c(0.6, 0.8, 1.2, 1.4, 1.6), 1)
      nuevo_distractor <- paste0(round(num_correcto * factor_aleatorio, 3), "/", denominador_correcto)

      if (!nuevo_distractor %in% distractores_validos &&
          nuevo_distractor != respuesta_correcta_fraccion) {
        distractores_validos <- c(distractores_validos, nuevo_distractor)
      }
    }
    return(distractores_validos[1:3])
  }
}

# Generar distractores desafiantes
distractores_generados <- generar_distractores_desafiantes(
  pregunta_seleccionada$numerador,
  pregunta_seleccionada$denominador
)

# Crear vector con todas las opciones
opciones_respuesta <- c(respuesta_correcta_fraccion, distractores_generados)
names(opciones_respuesta) <- c("correcta", "distractor1", "distractor2", "distractor3")

# VALIDACIÓN ROBUSTA DE OPCIONES ÚNICAS
test_that("Todas las opciones de respuesta son únicas", {
  expect_equal(length(unique(opciones_respuesta)), 4)
})

test_that("La respuesta correcta está en las opciones", {
  expect_true(respuesta_correcta_fraccion %in% opciones_respuesta)
})

# Mezclar las opciones aleatoriamente
opciones_mezcladas <- sample(opciones_respuesta)

# Identificar posición de la respuesta correcta
indice_correcto <- which(opciones_mezcladas == respuesta_correcta_fraccion)

test_that("Se encontró exactamente una respuesta correcta", {
  expect_equal(length(indice_correcto), 1)
  expect_true(indice_correcto >= 1 && indice_correcto <= 4)
})

# Crear vector de solución para r-exams
solucion_vector <- rep(0, 4)
solucion_vector[indice_correcto] <- 1

test_that("Vector de solución es válido", {
  expect_equal(length(solucion_vector), 4)
  expect_equal(sum(solucion_vector), 1)
})

# ALEATORIZACIÓN DEL DATO CONOCIDO EN EL ENUNCIADO (4 opciones)
datos_conocidos_disponibles <- list(
  list(valor = p_menor_masc, descripcion = paste(termino_masculino_seleccionado, termino_menores_seleccionado, "de", edad_corte_seleccionada, "años")),
  list(valor = p_menor_fem, descripcion = paste(termino_femenino_seleccionado, termino_menores_seleccionado, "de", edad_corte_seleccionada, "años")),
  list(valor = p_mayor_masc, descripcion = paste(termino_masculino_seleccionado, termino_mayores_seleccionado, "de", edad_corte_seleccionada, "años")),
  list(valor = p_mayor_fem, descripcion = paste(termino_femenino_seleccionado, termino_mayores_seleccionado, "de", edad_corte_seleccionada, "años"))
)

dato_conocido_seleccionado <- sample(datos_conocidos_disponibles, 1)[[1]]
porcentaje_conocido <- round(dato_conocido_seleccionado$valor * 100, 3)
```

```{r generar_tabla_contingencia_corregida, include=FALSE}
options(OutDec = ".")  # Asegurar punto decimal en este chunk

# ALEATORIZACIÓN AVANZADA DE COLORES Y ESTILOS VISUALES DE LA TABLA
paletas_colores_profesionales <- list(
  # Paleta corporativa azul
  list(fondo = "blue", intensidades = c(15, 20, 25), nombre = "corporativa_azul"),
  # Paleta académica verde
  list(fondo = "green", intensidades = c(18, 22, 28), nombre = "academica_verde"),
  # Paleta moderna naranja
  list(fondo = "orange", intensidades = c(12, 18, 24), nombre = "moderna_naranja"),
  # Paleta elegante púrpura
  list(fondo = "purple", intensidades = c(16, 20, 26), nombre = "elegante_purpura"),
  # Paleta tecnológica cyan
  list(fondo = "cyan", intensidades = c(14, 19, 25), nombre = "tecnologica_cyan"),
  # Paleta clásica gris
  list(fondo = "gray", intensidades = c(20, 25, 30), nombre = "clasica_gris"),
  # Paleta roja profesional
  list(fondo = "red", intensidades = c(10, 15, 20), nombre = "roja_profesional"),
  # Paleta violeta moderna
  list(fondo = "violet", intensidades = c(12, 18, 24), nombre = "violeta_moderna")
)

# Seleccionar paleta aleatoria
paleta_seleccionada <- sample(paletas_colores_profesionales, 1)[[1]]
color_fondo_seleccionado <- paleta_seleccionada$fondo
intensidad_seleccionada <- sample(paleta_seleccionada$intensidades, 1)
color_tabla_final <- paste0(color_fondo_seleccionado, "!", intensidad_seleccionada)

# Aleatorización de estilos de borde y formato
estilos_tabla_disponibles <- c("simple", "doble", "grueso", "elegante")
estilo_tabla_seleccionado <- sample(estilos_tabla_disponibles, 1)

# FUNCIÓN CORREGIDA PARA GENERAR TABLA TIKZ MATEMÁTICAMENTE COHERENTE
generar_tabla_contingencia_tikz_corregida <- function(termino_masc, termino_fem, termino_men, termino_may,
                                                     edad, p_men_masc, p_men_fem, p_may_masc, p_may_fem,
                                                     color_tabla, estilo_tabla = "simple") {

  # FORMATO ESTANDARIZADO DE NÚMEROS EN LA TABLA (3 decimales)
  # Mostrar como decimales con 3 cifras para mantener coherencia en todo el código
  val_men_masc <- sprintf("%.3f", p_men_masc)
  val_men_fem <- sprintf("%.3f", p_men_fem)
  val_may_masc <- sprintf("%.3f", p_may_masc)
  val_may_fem <- sprintf("%.3f", p_may_fem)

  # ALEATORIZACIÓN DE ESTILO DE BORDES
  if (estilo_tabla == "doble") {
    separador_linea <- "    \\hline\\hline"
    borde_tabla <- "|c||c|c|"
  } else if (estilo_tabla == "grueso") {
    separador_linea <- "    \\hline"
    borde_tabla <- "||c|c|c||"
  } else if (estilo_tabla == "elegante") {
    separador_linea <- "    \\hline"
    borde_tabla <- "|c|c|c|"
  } else {
    separador_linea <- "    \\hline"
    borde_tabla <- "|c|c|c|"
  }

  # Función para capitalizar primera letra
  capitalizar_primera <- function(texto) {
    paste0(toupper(substring(texto, 1, 1)), substring(texto, 2))
  }

  # Crear tabla con TikZ usando estructura corregida
  tabla_codigo <- c(
    "\\begin{tikzpicture}",
    "\\node[inner sep=0pt] {",
    paste0("  \\begin{tabular}{", borde_tabla, "}"),
    separador_linea,
    paste0("    \\rowcolor{", color_tabla, "}"),
    paste0("    \\textbf{Grupo de edad} & \\textbf{", capitalizar_primera(termino_masc), "} & \\textbf{", capitalizar_primera(termino_fem), "} \\\\"),
    separador_linea,
    paste0("    ", capitalizar_primera(termino_men), " de ", edad, " años & ", val_men_masc, " & ", val_men_fem, " \\\\"),
    separador_linea,
    paste0("    ", capitalizar_primera(termino_may), " de ", edad, " años & ", val_may_masc, " & ", val_may_fem, " \\\\"),
    separador_linea,
    "  \\end{tabular}",
    "};",
    "\\end{tikzpicture}"
  )

  tabla_codigo
}

# Generar código TikZ para la tabla de contingencia corregida
tabla_tikz_codigo <- generar_tabla_contingencia_tikz_corregida(
  termino_masculino_seleccionado, termino_femenino_seleccionado,
  termino_menores_seleccionado, termino_mayores_seleccionado,
  edad_corte_seleccionada, p_menor_masc, p_menor_fem, p_mayor_masc, p_mayor_fem,
  color_tabla_final, estilo_tabla_seleccionado
)

# VALIDACIONES FINALES DE COHERENCIA MATEMÁTICA
test_that("Código TikZ se generó correctamente", {
  expect_true(length(tabla_tikz_codigo) > 0)
  expect_true(any(grepl("tikzpicture", tabla_tikz_codigo)))
})

test_that("Coherencia matemática final de proporciones", {
  suma_total_final <- p_menor_masc + p_menor_fem + p_mayor_masc + p_mayor_fem
  expect_equal(suma_total_final, 1.0, tolerance = 0.001)
})

test_that("Todas las proporciones están en rango válido", {
  expect_true(all(c(p_menor_masc, p_menor_fem, p_mayor_masc, p_mayor_fem) > 0))
  expect_true(all(c(p_menor_masc, p_menor_fem, p_mayor_masc, p_mayor_fem) < 1))
})

test_that("Coherencia de términos de género", {
  expect_false(termino_masculino_seleccionado == termino_femenino_seleccionado)
})

test_that("Vector de solución final es válido", {
  expect_equal(length(solucion_vector), 4)
  expect_equal(sum(solucion_vector), 1)
  expect_true(all(solucion_vector %in% c(0, 1)))
})
```

Question
========

En el `r termino_tabla_seleccionado` se muestran los `r termino_proporciones_seleccionado` de `r termino_participantes_seleccionado` en un `r contexto_seleccionado`, dependiendo del género y la edad.

```{r mostrar_tabla_contingencia_corregida, echo=FALSE, results='asis', fig.align="center"}
# Detectar formato de salida siguiendo el patrón exitoso de ejemplos funcionales
formatos_moodle_disponibles <- c("exams2moodle", "exams2qti12", "exams2qti21", "exams2openolat")
es_formato_moodle <- (match_exams_call() %in% formatos_moodle_disponibles)

# Determinar ancho de tabla según formato
ancho_tabla_contingencia <- if(es_formato_moodle) "5cm" else "7cm"

# Función para capitalizar primera letra
capitalizar_primera_global <- function(texto) {
  paste0(toupper(substring(texto, 1, 1)), substring(texto, 2))
}

# Función para mostrar tabla con manejo robusto de errores
mostrar_tabla_tikz_segura <- function(codigo_tikz, ancho, nombre_tabla = "tabla_datos") {
  tryCatch({
    # Intentar usar include_tikz siguiendo el patrón exitoso de los ejemplos
    include_tikz(codigo_tikz,
                 name = nombre_tabla,
                 markup = "markdown",
                 format = typ,
                 packages = c("tikz", "colortbl"),
                 width = ancho)
  }, error = function(e) {
    # Fallback mejorado: tabla LaTeX bien formateada
    cat("\\begin{center}\n")
    cat("\\begin{tabular}{|c|c|c|}\n")
    cat("\\hline\n")
    cat("\\textbf{Grupo de edad} & \\textbf{", capitalizar_primera_global(termino_masculino_seleccionado), "} & \\textbf{", capitalizar_primera_global(termino_femenino_seleccionado), "} \\\\\n", sep="")
    cat("\\hline\n")
    cat(capitalizar_primera_global(termino_menores_seleccionado), " de ", edad_corte_seleccionada, " años & ", sprintf("%.3f", p_menor_masc), " & ", sprintf("%.3f", p_menor_fem), " \\\\\n", sep="")
    cat("\\hline\n")
    cat(capitalizar_primera_global(termino_mayores_seleccionado), " de ", edad_corte_seleccionada, " años & ", sprintf("%.3f", p_mayor_masc), " & ", sprintf("%.3f", p_mayor_fem), " \\\\\n", sep="")
    cat("\\hline\n")
    cat("\\end{tabular}\n")
    cat("\\end{center}\n")
  })
}

# Mostrar la tabla de contingencia
mostrar_tabla_tikz_segura(tabla_tikz_codigo, ancho_tabla_contingencia, "tabla_contingencia_corregida")
```

Por ejemplo, el `r porcentaje_conocido`% de los `r termino_participantes_seleccionado` son `r dato_conocido_seleccionado$descripcion`. Según el `r termino_tabla_seleccionado`, ¿cuál es la probabilidad de que al escoger una persona al azar pertenezca al grupo de los `r pregunta_seleccionada$texto_evento`, si ya se sabe que hace parte del grupo de `r pregunta_seleccionada$texto_condicion`?

Answerlist
----------
- `r opciones_mezcladas[1]`
- `r opciones_mezcladas[2]`
- `r opciones_mezcladas[3]`
- `r opciones_mezcladas[4]`

Solution
========

Para resolver este problema de **probabilidad condicional**, necesitamos aplicar correctamente la fórmula de probabilidad condicional y trabajar con la información de la tabla de contingencia.

### Paso 1: Identificar el tipo de problema y la fórmula
Este es un problema de **probabilidad condicional**, donde buscamos:

$$P(\text{`r pregunta_seleccionada$texto_evento`} | \text{`r pregunta_seleccionada$texto_condicion`})$$

La fórmula de probabilidad condicional es:
$$P(A|B) = \frac{P(A \cap B)}{P(B)}$$

Donde:

- $A$ = evento de interés: `r pregunta_seleccionada$texto_evento`
- $B$ = condición dada: `r pregunta_seleccionada$texto_condicion`
- $P(A \cap B)$ = probabilidad de que ocurran ambos eventos simultáneamente
- $P(B)$ = probabilidad marginal de la condición

### Paso 2: Extraer información de la tabla de contingencia
De la tabla podemos obtener las siguientes **probabilidades conjuntas**:

- P(`r termino_menores_seleccionado` de `r edad_corte_seleccionada` años $\cap$ `r termino_masculino_seleccionado`) = `r sprintf("%.3f", p_menor_masc)`
- P(`r termino_menores_seleccionado` de `r edad_corte_seleccionada` años $\cap$ `r termino_femenino_seleccionado`) = `r sprintf("%.3f", p_menor_fem)`
- P(`r termino_mayores_seleccionado` de `r edad_corte_seleccionada` años $\cap$ `r termino_masculino_seleccionado`) = `r sprintf("%.3f", p_mayor_masc)`
- P(`r termino_mayores_seleccionado` de `r edad_corte_seleccionada` años $\cap$ `r termino_femenino_seleccionado`) = `r sprintf("%.3f", p_mayor_fem)`

### Paso 3: Calcular probabilidades marginales
Las **probabilidades marginales** se obtienen sumando las probabilidades conjuntas:

**Por género:**

- P(`r termino_masculino_seleccionado`) = `r sprintf("%.3f", p_menor_masc)` + `r sprintf("%.3f", p_mayor_masc)` = `r sprintf("%.3f", p_masculino)`
- P(`r termino_femenino_seleccionado`) = `r sprintf("%.3f", p_menor_fem)` + `r sprintf("%.3f", p_mayor_fem)` = `r sprintf("%.3f", p_femenino)`

**Por edad:**

- P(`r termino_menores_seleccionado` de `r edad_corte_seleccionada` años) = `r sprintf("%.3f", p_menor_masc)` + `r sprintf("%.3f", p_menor_fem)` = `r sprintf("%.3f", p_menor)`
- P(`r termino_mayores_seleccionado` de `r edad_corte_seleccionada` años) = `r sprintf("%.3f", p_mayor_masc)` + `r sprintf("%.3f", p_mayor_fem)` = `r sprintf("%.3f", p_mayor)`

### Paso 4: Aplicar la fórmula para nuestro problema específico
Para nuestro problema:

$$P(\text{`r pregunta_seleccionada$texto_evento`} | \text{`r pregunta_seleccionada$texto_condicion`}) = \frac{P(\text{`r pregunta_seleccionada$texto_evento`} \cap \text{`r pregunta_seleccionada$texto_condicion`})}{P(\text{`r pregunta_seleccionada$texto_condicion`})}$$

Sustituyendo los valores de la tabla:
$$P(\text{`r pregunta_seleccionada$texto_evento`} | \text{`r pregunta_seleccionada$texto_condicion`}) = \frac{`r sprintf("%.3f", pregunta_seleccionada$numerador)`}{`r sprintf("%.3f", pregunta_seleccionada$denominador)`}$$

### Paso 5: Verificación y análisis de distractores
**Respuesta correcta:** `r respuesta_correcta_fraccion` = `r sprintf("%.3f", respuesta_correcta_decimal)`

**Análisis de errores conceptuales comunes (distractores):**

1. **`r distractores_generados[1]`**: Error de **invertir la probabilidad condicional** - confundir P(A|B) con P(B|A)
2. **`r distractores_generados[2]`**: Error de **no aplicar la condición** - usar solo la probabilidad conjunta sin dividir por la probabilidad de la condición
3. **`r distractores_generados[3]`**: Error de **usar probabilidad marginal incorrecta** o error de cálculo conceptual

### Conclusión
Por lo tanto, la probabilidad de que una persona pertenezca al grupo de `r pregunta_seleccionada$texto_evento`, dado que es `r pregunta_seleccionada$texto_condicion`, es **`r respuesta_correcta_fraccion`**.

Esta respuesta tiene sentido porque:

- El resultado está entre 0 y 1 (como toda probabilidad)
- El numerador representa la intersección de los dos eventos
- El denominador representa la probabilidad de la condición dada
- La interpretación es coherente con el contexto del problema

Answerlist
----------
- `r if(solucion_vector[1] == 1) "Verdadero" else "Falso"`
- `r if(solucion_vector[2] == 1) "Verdadero" else "Falso"`
- `r if(solucion_vector[3] == 1) "Verdadero" else "Falso"`
- `r if(solucion_vector[4] == 1) "Verdadero" else "Falso"`

Meta-information
================
exname: probabilidad_condicional_tabla_contingencia_corregido
extype: schoice
exsolution: `r paste(as.integer(solucion_vector), collapse="")`
exshuffle: TRUE
exsection: Probabilidad|Probabilidad condicional|Tablas de contingencia|Razonamiento matemático
exextra[Type]: Cálculo
exextra[Program]: R
exextra[Language]: es
exextra[Level]: 3
