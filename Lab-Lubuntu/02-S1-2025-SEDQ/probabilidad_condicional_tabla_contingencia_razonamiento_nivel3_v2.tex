% Options for packages loaded elsewhere
\PassOptionsToPackage{unicode}{hyperref}
\PassOptionsToPackage{hyphens}{url}
%
\documentclass[
]{article}
\usepackage{amsmath,amssymb}
\usepackage{iftex}
\ifPDFTeX
  \usepackage[T1]{fontenc}
  \usepackage[utf8]{inputenc}
  \usepackage{textcomp} % provide euro and other symbols
\else % if luatex or xetex
  \usepackage{unicode-math} % this also loads fontspec
  \defaultfontfeatures{Scale=MatchLowercase}
  \defaultfontfeatures[\rmfamily]{Ligatures=TeX,Scale=1}
\fi
\usepackage{lmodern}
\ifPDFTeX\else
  % xetex/luatex font selection
\fi
% Use upquote if available, for straight quotes in verbatim environments
\IfFileExists{upquote.sty}{\usepackage{upquote}}{}
\IfFileExists{microtype.sty}{% use microtype if available
  \usepackage[]{microtype}
  \UseMicrotypeSet[protrusion]{basicmath} % disable protrusion for tt fonts
}{}
\makeatletter
\@ifundefined{KOMAClassName}{% if non-KOMA class
  \IfFileExists{parskip.sty}{%
    \usepackage{parskip}
  }{% else
    \setlength{\parindent}{0pt}
    \setlength{\parskip}{6pt plus 2pt minus 1pt}}
}{% if KOMA class
  \KOMAoptions{parskip=half}}
\makeatother
\usepackage{xcolor}
\usepackage[margin=1in]{geometry}
\usepackage{graphicx}
\makeatletter
\newsavebox\pandoc@box
\newcommand*\pandocbounded[1]{% scales image to fit in text height/width
  \sbox\pandoc@box{#1}%
  \Gscale@div\@tempa{\textheight}{\dimexpr\ht\pandoc@box+\dp\pandoc@box\relax}%
  \Gscale@div\@tempb{\linewidth}{\wd\pandoc@box}%
  \ifdim\@tempb\p@<\@tempa\p@\let\@tempa\@tempb\fi% select the smaller of both
  \ifdim\@tempa\p@<\p@\scalebox{\@tempa}{\usebox\pandoc@box}%
  \else\usebox{\pandoc@box}%
  \fi%
}
% Set default figure placement to htbp
\def\fps@figure{htbp}
\makeatother
\setlength{\emergencystretch}{3em} % prevent overfull lines
\providecommand{\tightlist}{%
  \setlength{\itemsep}{0pt}\setlength{\parskip}{0pt}}
\setcounter{secnumdepth}{-\maxdimen} % remove section numbering
\usepackage{graphicx}
\usepackage{float}
\usepackage{bookmark}
\IfFileExists{xurl.sty}{\usepackage{xurl}}{} % add URL line breaks if available
\urlstyle{same}
\hypersetup{
  hidelinks,
  pdfcreator={LaTeX via pandoc}}

\author{}
\date{\vspace{-2.5em}}

\begin{document}

\section{Question}\label{question}

En el cuadro se muestran los datos estadísticos de estudiantes en un
curso vacacional, dependiendo del género y la edad.

\includegraphics[width=7cm,height=\textheight,keepaspectratio]{tabla_contingencia_corregida.png}

Por ejemplo, el 26.8\% de los estudiantes son asistentes masculinos
menores de 20 años. Según el cuadro, ¿cuál es la probabilidad de que al
escoger una persona al azar pertenezca al grupo de los mayores de 20
años, si ya se sabe que hace parte del grupo de personas de género
femenino?

\subsection{Answerlist}\label{answerlist}

\begin{itemize}
\tightlist
\item
  0.381/0.247
\item
  0.268/0.381
\item
  0.247/0.381
\item
  0.247/0.619
\end{itemize}

\section{Solution}\label{solution}

Para resolver este problema de \textbf{probabilidad condicional},
necesitamos aplicar correctamente la fórmula de probabilidad condicional
y trabajar con la información de la tabla de contingencia.

\subsubsection{Paso 1: Identificar el tipo de problema y la
fórmula}\label{paso-1-identificar-el-tipo-de-problema-y-la-fuxf3rmula}

Este es un problema de \textbf{probabilidad condicional}, donde
buscamos:

\[P(\text{mayores de 20 años} | \text{personas de género femenino})\]

La fórmula de probabilidad condicional es:
\[P(A|B) = \frac{P(A \cap B)}{P(B)}\]

Donde:

\begin{itemize}
\tightlist
\item
  \(A\) = evento de interés: mayores de 20 años
\item
  \(B\) = condición dada: personas de género femenino
\item
  \(P(A \cap B)\) = probabilidad de que ocurran ambos eventos
  simultáneamente
\item
  \(P(B)\) = probabilidad marginal de la condición
\end{itemize}

\subsubsection{Paso 2: Extraer información de la tabla de
contingencia}\label{paso-2-extraer-informaciuxf3n-de-la-tabla-de-contingencia}

De la tabla podemos obtener las siguientes \textbf{probabilidades
conjuntas}:

\begin{itemize}
\tightlist
\item
  P(menores de 20 años \(\cap\) asistentes masculinos) = 0.268
\item
  P(menores de 20 años \(\cap\) personas de género femenino) = 0.134
\item
  P(mayores de 20 años \(\cap\) asistentes masculinos) = 0.351
\item
  P(mayores de 20 años \(\cap\) personas de género femenino) = 0.247
\end{itemize}

\subsubsection{Paso 3: Calcular probabilidades
marginales}\label{paso-3-calcular-probabilidades-marginales}

Las \textbf{probabilidades marginales} se obtienen sumando las
probabilidades conjuntas:

\textbf{Por género:}

\begin{itemize}
\tightlist
\item
  P(asistentes masculinos) = 0.268 + 0.351 = 0.619
\item
  P(personas de género femenino) = 0.134 + 0.247 = 0.381
\end{itemize}

\textbf{Por edad:}

\begin{itemize}
\tightlist
\item
  P(menores de 20 años) = 0.268 + 0.134 = 0.402
\item
  P(mayores de 20 años) = 0.351 + 0.247 = 0.598
\end{itemize}

\subsubsection{Paso 4: Aplicar la fórmula para nuestro problema
específico}\label{paso-4-aplicar-la-fuxf3rmula-para-nuestro-problema-especuxedfico}

Para nuestro problema:

\[P(\text{mayores de 20 años} | \text{personas de género femenino}) = \frac{P(\text{mayores de 20 años} \cap \text{personas de género femenino})}{P(\text{personas de género femenino})}\]

Sustituyendo los valores de la tabla:
\[P(\text{mayores de 20 años} | \text{personas de género femenino}) = \frac{0.247}{0.381}\]

\subsubsection{Paso 5: Verificación y análisis de
distractores}\label{paso-5-verificaciuxf3n-y-anuxe1lisis-de-distractores}

\textbf{Respuesta correcta:} 0.247/0.381 = 0.648

\textbf{Análisis de errores conceptuales comunes (distractores):}

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\tightlist
\item
  \textbf{0.247/0.619}: Error de \textbf{condición incorrecta} - usar el
  numerador correcto pero con una probabilidad marginal diferente como
  denominador
\item
  \textbf{0.268/0.381}: Error de \textbf{evento incorrecto} - usar el
  denominador correcto pero con una probabilidad conjunta diferente como
  numerador
\item
  \textbf{0.381/0.247}: Error de \textbf{intercambio P(A\textbar B) con
  P(B\textbar A)} - confundir la dirección de la probabilidad
  condicional invirtiendo numerador y denominador
\end{enumerate}

\subsubsection{Conclusión}\label{conclusiuxf3n}

Por lo tanto, la probabilidad de que una persona pertenezca al grupo de
mayores de 20 años, dado que es personas de género femenino, es
\textbf{0.247/0.381}.

Esta respuesta tiene sentido porque:

\begin{itemize}
\tightlist
\item
  El resultado está entre 0 y 1 (como toda probabilidad)
\item
  El numerador representa la intersección de los dos eventos
\item
  El denominador representa la probabilidad de la condición dada
\item
  La interpretación es coherente con el contexto del problema
\end{itemize}

\subsection{Answerlist}\label{answerlist-1}

\begin{itemize}
\tightlist
\item
  Falso
\item
  Falso
\item
  Verdadero
\item
  Falso
\end{itemize}

\section{Meta-information}\label{meta-information}

exname: probabilidad\_condicional\_tabla\_contingencia\_corregido
extype: schoice exsolution: 0010 exshuffle: TRUE exsection:
Probabilidad\textbar Probabilidad condicional\textbar Tablas de
contingencia\textbar Razonamiento matemático exextra{[}Type{]}: Cálculo
exextra{[}Program{]}: R exextra{[}Language{]}: es exextra{[}Level{]}: 3

\end{document}
