This is XeTeX, Version 3.141592653-2.6-0.999996 (TeX Live 2024) (preloaded format=xelatex 2025.2.26)  7 MAR 2025 23:32
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**Geometria_vuelo.tex
(./Geometria_vuelo.tex
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/base/article.cls
Document Class: article 2024/06/29 v1.4n Standard LaTeX document class
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/base/size10.clo
File: size10.clo 2024/06/29 v1.4n Standard LaTeX file (size option)
)
\c@part=\count192
\c@section=\count193
\c@subsection=\count194
\c@subsubsection=\count195
\c@paragraph=\count196
\c@subparagraph=\count197
\c@figure=\count198
\c@table=\count199
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2024/11/05 v2.17t AMS math features
\@mathmargin=\skip51
For additional information on amsmath, use the `?' option.
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks17
\ex@=\dimen142
)) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen143
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count266
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count267
\leftroot@=\count268
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count269
\DOTSCASE@=\count270
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box52
\strutbox@=\box53
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen144
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count271
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count272
\dotsspace@=\muskip17
\c@parentequation=\count273
\dspbrk@lvl=\count274
\tag@help=\toks18
\row@=\count275
\column@=\count276
\maxfields@=\count277
\andhelp@=\toks19
\eqnshift@=\dimen145
\alignsep@=\dimen146
\tagshift@=\dimen147
\tagwidth@=\dimen148
\totwidth@=\dimen149
\lineht@=\dimen150
\@envbody=\toks20
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks21
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/unicode-math/unicode-math.sty (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2025-01-18 L3 programming layer (loader) 
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/l3backend/l3backend-xetex.def
File: l3backend-xetex.def 2024-05-08 L3 backend support: XeTeX
\g__graphics_track_int=\count278
\l__pdf_internal_box=\box54
\g__pdf_backend_annotation_int=\count279
\g__pdf_backend_link_int=\count280
))
Package: unicode-math 2023/08/13 v0.8r Unicode maths in XeLaTeX and LuaLaTeX
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/unicode-math/unicode-math-xetex.sty
Package: unicode-math-xetex 2023/08/13 v0.8r Unicode maths in XeLaTeX and LuaLaTeX
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/l3packages/xparse/xparse.sty
Package: xparse 2024-08-16 L3 Experimental document command parser
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/l3packages/l3keys2e/l3keys2e.sty
Package: l3keys2e 2024-08-16 LaTeX2e option processing using LaTeX3 keys
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/fontspec/fontspec.sty
Package: fontspec 2024/05/11 v2.9e Font selection for XeLaTeX and LuaLaTeX
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/fontspec/fontspec-xetex.sty
Package: fontspec-xetex 2024/05/11 v2.9e Font selection for XeLaTeX and LuaLaTeX
\l__fontspec_script_int=\count281
\l__fontspec_language_int=\count282
\l__fontspec_strnum_int=\count283
\l__fontspec_tmp_int=\count284
\l__fontspec_tmpa_int=\count285
\l__fontspec_tmpb_int=\count286
\l__fontspec_tmpc_int=\count287
\l__fontspec_em_int=\count288
\l__fontspec_emdef_int=\count289
\l__fontspec_strong_int=\count290
\l__fontspec_strongdef_int=\count291
\l__fontspec_tmpa_dim=\dimen151
\l__fontspec_tmpb_dim=\dimen152
\l__fontspec_tmpc_dim=\dimen153
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/fontspec/fontspec.cfg))) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/base/fix-cm.sty
Package: fix-cm 2020/11/24 v1.1t fixes to LaTeX
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/base/ts1enc.def
File: ts1enc.def 2001/06/05 v3.0e (jk/car/fm) Standard LaTeX file
LaTeX Font Info:    Redeclaring font encoding TS1 on input line 47.
))
\g__um_fam_int=\count292
\g__um_fonts_used_int=\count293
\l__um_primecount_int=\count294
\g__um_primekern_muskip=\muskip18
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/unicode-math/unicode-math-table.tex))) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/lm/lmodern.sty
Package: lmodern 2015/05/01 v1.6.1 Latin Modern Fonts
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> OT1/lmr/m/n on input line 22.
LaTeX Font Info:    Overwriting symbol font `letters' in version `normal'
(Font)                  OML/cmm/m/it --> OML/lmm/m/it on input line 23.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `normal'
(Font)                  OMS/cmsy/m/n --> OMS/lmsy/m/n on input line 24.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `normal'
(Font)                  OMX/cmex/m/n --> OMX/lmex/m/n on input line 25.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/lmr/bx/n on input line 26.
LaTeX Font Info:    Overwriting symbol font `letters' in version `bold'
(Font)                  OML/cmm/b/it --> OML/lmm/b/it on input line 27.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `bold'
(Font)                  OMS/cmsy/b/n --> OMS/lmsy/b/n on input line 28.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `bold'
(Font)                  OMX/cmex/m/n --> OMX/lmex/m/n on input line 29.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> OT1/lmr/bx/n on input line 31.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> OT1/lmss/m/n on input line 32.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> OT1/lmr/m/it on input line 33.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> OT1/lmtt/m/n on input line 34.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/lmr/bx/n on input line 35.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> OT1/lmss/bx/n on input line 36.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> OT1/lmr/bx/it on input line 37.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> OT1/lmtt/m/n on input line 38.
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: xetex.def on input line 274.
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/graphics-def/xetex.def
File: xetex.def 2022/09/22 v5.0n Graphics/color driver for xetex
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `RGB' extended on input line 1365.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1367.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1371.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1372.
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks22
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
)
\Gm@cnth=\count295
\Gm@cntv=\count296
\c@Gm@tempcnt=\count297
\Gm@bindingoffset=\dimen154
\Gm@wd@mp=\dimen155
\Gm@odd@mp=\dimen156
\Gm@even@mp=\dimen157
\Gm@layoutwidth=\dimen158
\Gm@layoutheight=\dimen159
\Gm@layouthoffset=\dimen160
\Gm@layoutvoffset=\dimen161
\Gm@dimlist=\toks23
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: xetex.def on input line 106.
)
\Gin@req@height=\dimen162
\Gin@req@width=\dimen163
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/babel/babel.sty
Package: babel 2025/02/14 v25.4 The multilingual framework for pdfLaTeX, LuaLaTeX and XeLaTeX
\babel@savecnt=\count298
\U@D=\dimen164
\l@unhyphenated=\language6
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/babel/xebabel.def)
\bbl@readstream=\read2
\bbl@dirlevel=\count299
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/babel-spanish/spanish.ldf
Language: spanish.ldf 2021/05/27 v5.0q Spanish support from the babel system
\es@quottoks=\toks24
\es@quotdepth=\count300
Package babel Info: Making " an active character on input line 570.
Package babel Info: Making . an active character on input line 675.
Package babel Info: Making < an active character on input line 722.
Package babel Info: Making > an active character on input line 722.
)) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/babel/locale/es/babel-spanish.tex
Package babel Info: Importing font and identification data for spanish
(babel)             from babel-es.ini. Reported on input line 11.
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/pgf/frontendlayer/tikz.sty (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/pgf/basiclayer/pgf.sty (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/pgf/utilities/pgfrcs.sty (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/utilities/pgfutil-common.tex
\pgfutil@everybye=\toks25
\pgfutil@tempdima=\dimen165
\pgfutil@tempdimb=\dimen166
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/utilities/pgfutil-latex.def
\pgfutil@abb=\box55
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/utilities/pgfrcs.code.tex (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/pgf.revision.tex)
Package: pgfrcs 2023-01-15 v3.1.10 (3.1.10)
))
Package: pgf 2023-01-15 v3.1.10 (3.1.10)
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/pgf/basiclayer/pgfcore.sty (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/pgf/systemlayer/pgfsys.sty (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/systemlayer/pgfsys.code.tex
Package: pgfsys 2023-01-15 v3.1.10 (3.1.10)
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex
\pgfkeys@pathtoks=\toks26
\pgfkeys@temptoks=\toks27
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/utilities/pgfkeyslibraryfiltered.code.tex
\pgfkeys@tmptoks=\toks28
))
\pgf@x=\dimen167
\pgf@y=\dimen168
\pgf@xa=\dimen169
\pgf@ya=\dimen170
\pgf@xb=\dimen171
\pgf@yb=\dimen172
\pgf@xc=\dimen173
\pgf@yc=\dimen174
\pgf@xd=\dimen175
\pgf@yd=\dimen176
\w@pgf@writea=\write3
\r@pgf@reada=\read3
\c@pgf@counta=\count301
\c@pgf@countb=\count302
\c@pgf@countc=\count303
\c@pgf@countd=\count304
\t@pgf@toka=\toks29
\t@pgf@tokb=\toks30
\t@pgf@tokc=\toks31
\pgf@sys@id@count=\count305
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/systemlayer/pgf.cfg
File: pgf.cfg 2023-01-15 v3.1.10 (3.1.10)
)
Driver file for pgf: pgfsys-xetex.def
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-xetex.def
File: pgfsys-xetex.def 2023-01-15 v3.1.10 (3.1.10)
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-dvipdfmx.def
File: pgfsys-dvipdfmx.def 2023-01-15 v3.1.10 (3.1.10)
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-common-pdf.def
File: pgfsys-common-pdf.def 2023-01-15 v3.1.10 (3.1.10)
)
\pgfsys@objnum=\count306
))) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/systemlayer/pgfsyssoftpath.code.tex
File: pgfsyssoftpath.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfsyssoftpath@smallbuffer@items=\count307
\pgfsyssoftpath@bigbuffer@items=\count308
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/systemlayer/pgfsysprotocol.code.tex
File: pgfsysprotocol.code.tex 2023-01-15 v3.1.10 (3.1.10)
)) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcore.code.tex
Package: pgfcore 2023-01-15 v3.1.10 (3.1.10)
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathutil.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathparser.code.tex
\pgfmath@dimen=\dimen177
\pgfmath@count=\count309
\pgfmath@box=\box56
\pgfmath@toks=\toks32
\pgfmath@stack@operand=\toks33
\pgfmath@stack@operation=\toks34
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.basic.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.trigonometric.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.random.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.comparison.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.base.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.round.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.misc.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.integerarithmetics.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathcalc.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfloat.code.tex
\c@pgfmathroundto@lastzeros=\count310
)) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfint.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepoints.code.tex
File: pgfcorepoints.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@picminx=\dimen178
\pgf@picmaxx=\dimen179
\pgf@picminy=\dimen180
\pgf@picmaxy=\dimen181
\pgf@pathminx=\dimen182
\pgf@pathmaxx=\dimen183
\pgf@pathminy=\dimen184
\pgf@pathmaxy=\dimen185
\pgf@xx=\dimen186
\pgf@xy=\dimen187
\pgf@yx=\dimen188
\pgf@yy=\dimen189
\pgf@zx=\dimen190
\pgf@zy=\dimen191
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathconstruct.code.tex
File: pgfcorepathconstruct.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@path@lastx=\dimen192
\pgf@path@lasty=\dimen193
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathusage.code.tex
File: pgfcorepathusage.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@shorten@end@additional=\dimen194
\pgf@shorten@start@additional=\dimen195
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorescopes.code.tex
File: pgfcorescopes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfpic=\box57
\pgf@hbox=\box58
\pgf@layerbox@main=\box59
\pgf@picture@serial@count=\count311
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcoregraphicstate.code.tex
File: pgfcoregraphicstate.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgflinewidth=\dimen196
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransformations.code.tex
File: pgfcoretransformations.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@pt@x=\dimen197
\pgf@pt@y=\dimen198
\pgf@pt@temp=\dimen199
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorequick.code.tex
File: pgfcorequick.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreobjects.code.tex
File: pgfcoreobjects.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathprocessing.code.tex
File: pgfcorepathprocessing.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorearrows.code.tex
File: pgfcorearrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowsep=\dimen256
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreshade.code.tex
File: pgfcoreshade.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@max=\dimen257
\pgf@sys@shading@range@num=\count312
\pgf@shadingcount=\count313
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreimage.code.tex
File: pgfcoreimage.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreexternal.code.tex
File: pgfcoreexternal.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfexternal@startupbox=\box60
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorelayers.code.tex
File: pgfcorelayers.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransparency.code.tex
File: pgfcoretransparency.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepatterns.code.tex
File: pgfcorepatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorerdf.code.tex
File: pgfcorerdf.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/modules/pgfmoduleshapes.code.tex
File: pgfmoduleshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodeparttextbox=\box61
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/modules/pgfmoduleplot.code.tex
File: pgfmoduleplot.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-0-65.sty
Package: pgfcomp-version-0-65 2023-01-15 v3.1.10 (3.1.10)
\pgf@nodesepstart=\dimen258
\pgf@nodesepend=\dimen259
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-1-18.sty
Package: pgfcomp-version-1-18 2023-01-15 v3.1.10 (3.1.10)
)) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/pgf/utilities/pgffor.sty (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/pgf/utilities/pgfkeys.sty (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex)) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/pgf/math/pgfmath.sty (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex)) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/utilities/pgffor.code.tex
Package: pgffor 2023-01-15 v3.1.10 (3.1.10)
\pgffor@iter=\dimen260
\pgffor@skip=\dimen261
\pgffor@stack=\toks35
\pgffor@toks=\toks36
)) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/frontendlayer/tikz/tikz.code.tex
Package: tikz 2023-01-15 v3.1.10 (3.1.10)
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/libraries/pgflibraryplothandlers.code.tex
File: pgflibraryplothandlers.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@plot@mark@count=\count314
\pgfplotmarksize=\dimen262
)
\tikz@lastx=\dimen263
\tikz@lasty=\dimen264
\tikz@lastxsaved=\dimen265
\tikz@lastysaved=\dimen266
\tikz@lastmovetox=\dimen267
\tikz@lastmovetoy=\dimen268
\tikzleveldistance=\dimen269
\tikzsiblingdistance=\dimen270
\tikz@figbox=\box62
\tikz@figbox@bg=\box63
\tikz@tempbox=\box64
\tikz@tempbox@bg=\box65
\tikztreelevel=\count315
\tikznumberofchildren=\count316
\tikznumberofcurrentchild=\count317
\tikz@fig@count=\count318
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/modules/pgfmodulematrix.code.tex
File: pgfmodulematrix.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfmatrixcurrentrow=\count319
\pgfmatrixcurrentcolumn=\count320
\pgf@matrix@numberofcolumns=\count321
)
\tikz@expandcount=\count322
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrarytopaths.code.tex
File: tikzlibrarytopaths.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/bookmark/bookmark.sty
Package: bookmark 2023-12-10 v1.31 PDF bookmarks (HO)
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2024-11-05 v7.01l Hypertext links for LaTeX
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode not found.
)) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/gettitlestring/gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count323
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2025/02/11 v2.5l e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count324
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/stringenc/stringenc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO)
)
\@linkdim=\dimen271
\Hy@linkcounter=\count325
\Hy@pagecounter=\count326
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2024-11-05 v7.01l Hyperref: PDFDocEncoding definition (HO)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count327
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2024-11-05 v7.01l Hyperref: PDF Unicode definition (HO)
)
Package hyperref Info: Option `unicode' set `true' on input line 4040.
Package hyperref Info: Hyper figures OFF on input line 4157.
Package hyperref Info: Link nesting OFF on input line 4162.
Package hyperref Info: Hyper index ON on input line 4165.
Package hyperref Info: Plain pages OFF on input line 4172.
Package hyperref Info: Backreferencing OFF on input line 4177.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4424.
\c@Hy@tempcnt=\count328
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip19
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4763.
\XeTeXLinkMargin=\dimen272
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/bigintcalc/bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO)
))
\Fld@menulength=\count329
\Field@Width=\dimen273
\Fld@charsize=\dimen274
Package hyperref Info: Hyper figures OFF on input line 6042.
Package hyperref Info: Link nesting OFF on input line 6047.
Package hyperref Info: Hyper index ON on input line 6050.
Package hyperref Info: backreferencing OFF on input line 6057.
Package hyperref Info: Link coloring OFF on input line 6062.
Package hyperref Info: Link coloring with OCG OFF on input line 6067.
Package hyperref Info: PDF/A mode OFF on input line 6072.
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/base/atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count330
\c@Item=\count331
\c@Hfootnote=\count332
)
Package hyperref Info: Driver (autodetected): hxetex.
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/hyperref/hxetex.def
File: hxetex.def 2024-11-05 v7.01l Hyperref driver for XeTeX
\pdfm@box=\box66
\c@Hy@AnnotLevel=\count333
\HyField@AnnotCount=\count334
\Fld@listcount=\count335
\c@bookmark@seq@number=\count336
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/rerunfilecheck/rerunfilecheck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/base/atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend package
with kernel methods
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/uniquecounter/uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 285.
)
\Hy@SectionHShift=\skip54
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/bookmark/bkm-dvipdfm.def
File: bkm-dvipdfm.def 2023-12-10 v1.31 bookmark driver for dvipdfm (HO)
\BKM@id=\count337
))
No file Geometria_vuelo.aux.
\openout1 = `Geometria_vuelo.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 73.
LaTeX Font Info:    ... okay on input line 73.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 73.
LaTeX Font Info:    ... okay on input line 73.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 73.
LaTeX Font Info:    ... okay on input line 73.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 73.
LaTeX Font Info:    ... okay on input line 73.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 73.
LaTeX Font Info:    ... okay on input line 73.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 73.
LaTeX Font Info:    ... okay on input line 73.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 73.
LaTeX Font Info:    ... okay on input line 73.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 73.
LaTeX Font Info:    ... okay on input line 73.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 73.
LaTeX Font Info:    ... okay on input line 73.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 73.
LaTeX Font Info:    ... okay on input line 73.
LaTeX Font Info:    Overwriting math alphabet `\mathrm' in version `normal'
(Font)                  OT1/lmr/m/n --> TU/lmr/m/n on input line 73.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/lmr/m/it --> TU/lmr/m/it on input line 73.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/lmr/bx/n --> TU/lmr/bx/n on input line 73.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/lmss/m/n --> TU/lmss/m/n on input line 73.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/lmss/bx/n --> TU/lmss/bx/n on input line 73.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/lmtt/m/n --> TU/lmtt/m/n on input line 73.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/lmtt/m/n --> TU/lmtt/bx/n on input line 73.

Package fontspec Info: 
(fontspec)             latinmodern-math scale = 0.9999964596882403.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 0.9999964596882403.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 0.9999964596882403.


Package fontspec Info: 
(fontspec)             Font family 'latinmodern-math.otf(0)' created for font
(fontspec)             'latinmodern-math.otf' with options
(fontspec)             [Scale=MatchLowercase,BoldItalicFont={},ItalicFont={},SmallCapsFont={},Script=Math,BoldFont={latinmodern-math.otf}].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->s*[0.9999964596882403]"[latinmodern-math.otf]/OT:script=math;language=dflt;"
(fontspec)             - 'bold' (b/n) with NFSS spec.:
(fontspec)             <->s*[0.9999964596882403]"[latinmodern-math.otf]/OT:script=math;language=dflt;"

LaTeX Font Info:    Font shape `TU/latinmodern-math.otf(0)/m/n' will be
(Font)              scaled to size 10.0pt on input line 73.

Package fontspec Info: 
(fontspec)             latinmodern-math scale = 0.9999964596882403.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 0.9999964596882403.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 0.9999964596882403.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 0.9999964596882403.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 0.9999964596882403.


Package fontspec Info: 
(fontspec)             Font family 'latinmodern-math.otf(1)' created for font
(fontspec)             'latinmodern-math.otf' with options
(fontspec)             [Scale=MatchLowercase,BoldItalicFont={},ItalicFont={},SmallCapsFont={},Script=Math,SizeFeatures={{Size=8.5-},{Size=6-8.5,Font=latinmodern-math.otf,Style=MathScript},{Size=-6,Font=latinmodern-math.otf,Style=MathScriptScript}},BoldFont={latinmodern-math.otf}].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <8.5->s*[0.9999964596882403]"[latinmodern-math.otf]/OT:script=math;language=dflt;"<6-8.5>s*[0.9999964596882403]"[latinmodern-math.otf]/OT:script=math;language=dflt;+ssty=0;"<-6>s*[0.9999964596882403]"[latinmodern-math.otf]/OT:script=math;language=dflt;+ssty=1;"
(fontspec)             - 'bold' (b/n) with NFSS spec.:
(fontspec)             <->s*[0.9999964596882403]"[latinmodern-math.otf]/OT:script=math;language=dflt;"

LaTeX Font Info:    Font shape `TU/latinmodern-math.otf(1)/m/n' will be
(Font)              scaled to size 10.0pt on input line 73.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `normal' on input line 73.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/lmr/m/n --> TU/latinmodern-math.otf(1)/m/n on input line 73.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `bold' on input line 73.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/lmr/bx/n --> TU/latinmodern-math.otf(1)/b/n on input line 73.

Package fontspec Info: 
(fontspec)             latinmodern-math scale = 0.9999964596882403.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 1.000096459334209.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 0.9999964596882403.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 1.000096459334209.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 0.9999964596882403.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 1.000096459334209.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 0.9999964596882403.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 1.000096459334209.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 0.9999964596882403.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 1.000096459334209.


Package fontspec Info: 
(fontspec)             Font family 'latinmodern-math.otf(2)' created for font
(fontspec)             'latinmodern-math.otf' with options
(fontspec)             [Scale=MatchLowercase,BoldItalicFont={},ItalicFont={},SmallCapsFont={},Script=Math,SizeFeatures={{Size=8.5-},{Size=6-8.5,Font=latinmodern-math.otf,Style=MathScript},{Size=-6,Font=latinmodern-math.otf,Style=MathScriptScript}},BoldFont={latinmodern-math.otf},ScaleAgain=1.0001,FontAdjustment={\fontdimen
(fontspec)             8\font =6.77pt\relax \fontdimen 9\font =3.94pt\relax
(fontspec)             \fontdimen 10\font =4.44pt\relax \fontdimen 11\font
(fontspec)             =6.86pt\relax \fontdimen 12\font =3.45pt\relax
(fontspec)             \fontdimen 13\font =3.63pt\relax \fontdimen 14\font
(fontspec)             =3.63pt\relax \fontdimen 15\font =2.89pt\relax
(fontspec)             \fontdimen 16\font =2.47pt\relax \fontdimen 17\font
(fontspec)             =2.47pt\relax \fontdimen 18\font =2.5pt\relax
(fontspec)             \fontdimen 19\font =2.0pt\relax \fontdimen 22\font
(fontspec)             =2.5pt\relax \fontdimen 20\font =0pt\relax \fontdimen
(fontspec)             21\font =0pt\relax }].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <8.5->s*[1.000096459334209]"[latinmodern-math.otf]/OT:script=math;language=dflt;"<6-8.5>s*[1.000096459334209]"[latinmodern-math.otf]/OT:script=math;language=dflt;+ssty=0;"<-6>s*[1.000096459334209]"[latinmodern-math.otf]/OT:script=math;language=dflt;+ssty=1;"
(fontspec)             - 'bold' (b/n) with NFSS spec.:
(fontspec)             <->s*[1.000096459334209]"[latinmodern-math.otf]/OT:script=math;language=dflt;"

LaTeX Font Info:    Encoding `OMS' has changed to `TU' for symbol font
(Font)              `symbols' in the math version `normal' on input line 73.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `normal'
(Font)                  OMS/lmsy/m/n --> TU/latinmodern-math.otf(2)/m/n on input line 73.
LaTeX Font Info:    Encoding `OMS' has changed to `TU' for symbol font
(Font)              `symbols' in the math version `bold' on input line 73.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `bold'
(Font)                  OMS/lmsy/b/n --> TU/latinmodern-math.otf(2)/b/n on input line 73.

Package fontspec Info: 
(fontspec)             latinmodern-math scale = 0.9999964596882403.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 0.9998964600422715.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 0.9999964596882403.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 0.9998964600422715.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 0.9999964596882403.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 0.9998964600422715.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 0.9999964596882403.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 0.9998964600422715.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 0.9999964596882403.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 0.9998964600422715.


Package fontspec Info: 
(fontspec)             Font family 'latinmodern-math.otf(3)' created for font
(fontspec)             'latinmodern-math.otf' with options
(fontspec)             [Scale=MatchLowercase,BoldItalicFont={},ItalicFont={},SmallCapsFont={},Script=Math,SizeFeatures={{Size=8.5-},{Size=6-8.5,Font=latinmodern-math.otf,Style=MathScript},{Size=-6,Font=latinmodern-math.otf,Style=MathScriptScript}},BoldFont={latinmodern-math.otf},ScaleAgain=0.9999,FontAdjustment={\fontdimen
(fontspec)             8\font =0.4pt\relax \fontdimen 9\font =2.0pt\relax
(fontspec)             \fontdimen 10\font =1.67pt\relax \fontdimen 11\font
(fontspec)             =1.11pt\relax \fontdimen 12\font =6.0pt\relax
(fontspec)             \fontdimen 13\font =0pt\relax }].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <8.5->s*[0.9998964600422715]"[latinmodern-math.otf]/OT:script=math;language=dflt;"<6-8.5>s*[0.9998964600422715]"[latinmodern-math.otf]/OT:script=math;language=dflt;+ssty=0;"<-6>s*[0.9998964600422715]"[latinmodern-math.otf]/OT:script=math;language=dflt;+ssty=1;"
(fontspec)             - 'bold' (b/n) with NFSS spec.:
(fontspec)             <->s*[0.9998964600422715]"[latinmodern-math.otf]/OT:script=math;language=dflt;"

LaTeX Font Info:    Encoding `OMX' has changed to `TU' for symbol font
(Font)              `largesymbols' in the math version `normal' on input line 73.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `normal'
(Font)                  OMX/lmex/m/n --> TU/latinmodern-math.otf(3)/m/n on input line 73.
LaTeX Font Info:    Encoding `OMX' has changed to `TU' for symbol font
(Font)              `largesymbols' in the math version `bold' on input line 73.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `bold'
(Font)                  OMX/lmex/m/n --> TU/latinmodern-math.otf(3)/b/n on input line 73.
*geometry* driver: auto-detecting
*geometry* detected driver: xetex
*geometry* verbose mode - [ preamble ] result:
* driver: xetex
* paper: <default>
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(72.26999pt, 469.75502pt, 72.26999pt)
* v-part:(T,H,B)=(72.26999pt, 650.43001pt, 72.26999pt)
* \paperwidth=614.295pt
* \paperheight=794.96999pt
* \textwidth=469.75502pt
* \textheight=650.43001pt
* \oddsidemargin=0.0pt
* \evensidemargin=0.0pt
* \topmargin=-37.0pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=10.0pt
* \footskip=30.0pt
* \marginparwidth=65.0pt
* \marginparsep=11.0pt
* \columnsep=10.0pt
* \skip\footins=9.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

LaTeX Info: Redefining \. on input line 73.
LaTeX Info: Redefining \% on input line 73.
Package hyperref Info: Link coloring OFF on input line 73.

Package hyperref Warning: Rerun to get /PageLabels entry.

File: /tmp/RtmpmpGi9u/grafica_opcion_1.png Graphic file (type bmp)
</tmp/RtmpmpGi9u/grafica_opcion_1.png>
File: /tmp/RtmpmpGi9u/grafica_opcion_2.png Graphic file (type bmp)
</tmp/RtmpmpGi9u/grafica_opcion_2.png>
File: /tmp/RtmpmpGi9u/grafica_opcion_3.png Graphic file (type bmp)
</tmp/RtmpmpGi9u/grafica_opcion_3.png>
File: /tmp/RtmpmpGi9u/grafica_opcion_4.png Graphic file (type bmp)
</tmp/RtmpmpGi9u/grafica_opcion_4.png>


[1

]

[2]
! Text line contains an invalid character.
l.143 ... - Subida constante hasta 25 segundos ^^S
                                                   - Descenso 
Here is how much of TeX's memory you used:
 27949 strings out of 475832
 548433 string characters out of 5773476
 1067646 words of memory out of 5000000
 50523 multiletter control sequences out of 15000+600000
 559505 words of font info for 47 fonts, out of 8000000 for 9000
 14 hyphenation exceptions out of 8191
 90i,5n,114p,414b,434s stack positions out of 10000i,1000n,20000p,200000b,200000s

Output written on Geometria_vuelo.pdf (2 pages).
