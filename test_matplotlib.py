#!/usr/bin/env python3
import matplotlib
matplotlib.use('Agg')  # Usar backend no interactivo
import matplotlib.pyplot as plt
import numpy as np

print("numpy version:", np.__version__)
print("matplotlib version:", matplotlib.__version__)
print("Backend configurado:", matplotlib.get_backend())

# Crear un gráfico simple de prueba
x = np.linspace(0, 10, 100)
y = np.sin(x)

plt.figure(figsize=(8, 6))
plt.plot(x, y, 'b-', linewidth=2, label='sin(x)')
plt.xlabel('x')
plt.ylabel('y')
plt.title('Gráfico de prueba - sin(x)')
plt.legend()
plt.grid(True)

# Guardar el gráfico
plt.savefig('test_plot.png', dpi=150, bbox_inches='tight')
plt.close()

print("¡Gráfico de prueba creado exitosamente como 'test_plot.png'!")
print("¡Matplotlib está funcionando correctamente!")
