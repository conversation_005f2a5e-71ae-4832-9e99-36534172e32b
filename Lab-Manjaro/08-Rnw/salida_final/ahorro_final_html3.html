<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>

<head>
<title>Exam 3</title>
<style type="text/css">
body{font-family: Arial, Helvetica, Sans;}
</style>
<meta charset="utf-8" />
</head>

<body>
<h2>Exam 3</h2>

<ol>
<li>
<h4>Question</h4>
Natalia quiere ahorrar $125.000 cada mes durante 5 meses. Como ayuda para su inversi&lt;U+00F3&#62;n, sus padres le han propuesto dos opciones, pero solo puede elegir una de ellas.
<div class="p"><!----></div>
<b>Opci&lt;U+00F3&#62;n 1.</b> Al finalizar cada mes, su abuelo le regala un porcentaje del dinero que tenga acumulado en su inversi&lt;U+00F3&#62;n.
<div class="p"><!----></div>
<img src="tabla_opcion1.svg" alt="tabla_opcion1.svg" />
<b>Opci&lt;U+00F3&#62;n 2.</b> Al finalizar cada mes, su abuela le regala un porcentaje del dinero que tenga acumulado en su inversi&lt;U+00F3&#62;n.
<div class="p"><!----></div>
<img src="tabla_opcion2.svg" alt="tabla_opcion2.svg" />
Natalia decide elegir la opci&lt;U+00F3&#62;n en que le regalen la mayor cantidad de dinero y elija la abuelo para la elecci&lt;U+00F3&#62;n de Natalia?
<div class="p"><!----></div>
<br/>
<ol type="a">
<li>
No, porque en el &lt;U+00FA&#62;ltimo mes la ayuda del abuela es de <math xmlns="http://www.w3.org/1998/Math/MathML">
<mrow><mn>50</mn><mo>.</mo><mn>000</mn><mi>y</mi><mi mathvariant="italic">la</mi><mi mathvariant="italic">de</mi><mi mathvariant="italic">la</mi><mi mathvariant="italic">abuelo</mi><mi mathvariant="italic">es</mi><mi mathvariant="italic">de</mi></mrow></math>62.500.
</li>
<li>
S&lt;U+00ED&#62;, porque la ayuda total de su abuelo es de <math xmlns="http://www.w3.org/1998/Math/MathML">
<mrow><mn>187</mn><mo>.</mo><mn>500</mn><mi mathvariant="italic">mientras</mi><mi mathvariant="italic">que</mi><mi mathvariant="italic">la</mi><mi mathvariant="italic">de</mi><mi mathvariant="italic">su</mi><mi mathvariant="italic">abuela</mi><mi mathvariant="italic">es</mi><mi mathvariant="italic">de</mi></mrow></math>171.250.
</li>
<li>
No, porque la suma de porcentajes del abuela (1+6+16+9+8=40
</li>
<li>
No, porque el porcentaje m&lt;U+00E1&#62;ximo del abuela es 16
</li>
</ol>
<br/>
<h4>Solution</h4>
<div class="p"><!----></div>
Para resolver este problema, debemos calcular la ayuda total que recibir&lt;U+00ED&#62;a Natalia con cada opci&lt;U+00F3&#62;n.
<div class="p"><!----></div>
<b>Opci&lt;U+00F3&#62;n 1 (abuelo):</b>
<ul>
<li> Mes 1: $125.000 <math xmlns="http://www.w3.org/1998/Math/MathML">
<mrow><mo>&times;</mo></mrow></math> 10% = $12.500
<div class="p"><!----></div>
</li>
<li> Mes 2: $250.000 <math xmlns="http://www.w3.org/1998/Math/MathML">
<mrow><mo>&times;</mo></mrow></math> 10% = $25.000
<div class="p"><!----></div>
</li>
<li> Mes 3: $375.000 <math xmlns="http://www.w3.org/1998/Math/MathML">
<mrow><mo>&times;</mo></mrow></math> 10% = $37.500
<div class="p"><!----></div>
</li>
</ul>
Total abuelo: $187.500
<div class="p"><!----></div>
<b>Opci&lt;U+00F3&#62;n 2 (abuela):</b>
<ul>
<li> Mes 1: $125.000 <math xmlns="http://www.w3.org/1998/Math/MathML">
<mrow><mo>&times;</mo></mrow></math> 1% = $1.250
<div class="p"><!----></div>
</li>
<li> Mes 2: $250.000 <math xmlns="http://www.w3.org/1998/Math/MathML">
<mrow><mo>&times;</mo></mrow></math> 6% = $15.000
<div class="p"><!----></div>
</li>
<li> Mes 3: $375.000 <math xmlns="http://www.w3.org/1998/Math/MathML">
<mrow><mo>&times;</mo></mrow></math> 16% = $60.000
<div class="p"><!----></div>
</li>
</ul>
Total abuela: $171.250
<div class="p"><!----></div>
Por lo tanto, la respuesta correcta es la opci&lt;U+00F3&#62;n que indica que la abuelo da m&lt;U+00E1&#62;s ayuda.
<div class="p"><!----></div>
<b>An&lt;U+00E1&#62;lisis de errores comunes:</b>
<ul>
<li> <b>Error 1</b>: Confundir porcentaje promedio con c&lt;U+00E1&#62;lculo real sobre montos acumulados
<div class="p"><!----></div>
</li>
<li> <b>Error 2</b>: Considerar solo el &lt;U+00FA&#62;ltimo mes en lugar del total acumulado
<div class="p"><!----></div>
</li>
<li> <b>Error 3</b>: Sumar porcentajes directamente sin considerar las bases de c&lt;U+00E1&#62;lculo
<div class="p"><!----></div>
</li>
<li> <b>Error 4</b>: Invertir la conclusi&lt;U+00F3&#62;n por lectura incorrecta de los totales
<div class="p"><!----></div>
</li>
</ul>
<div class="p"><!----></div>
<br/>
<ol type="a">
<li>
Falso. Esta opci&lt;U+00F3&#62;n contiene un error en el razonamiento o c&lt;U+00E1&#62;lculo matem&lt;U+00E1&#62;tico.
</li>
<li>
Verdadero. Esta opci&lt;U+00F3&#62;n calcula correctamente los totales de ayuda para ambas opciones y compara adecuadamente cu&lt;U+00E1&#62;l es mayor.
</li>
<li>
Falso. Esta opci&lt;U+00F3&#62;n suma porcentajes directamente sin considerar que se aplican sobre diferentes bases de c&lt;U+00E1&#62;lculo.
</li>
<li>
Falso. Esta opci&lt;U+00F3&#62;n contiene un error en el razonamiento o c&lt;U+00E1&#62;lculo matem&lt;U+00E1&#62;tico.
</li>
</ol>
<br/>
</li>
</ol>

</body>
</html>
