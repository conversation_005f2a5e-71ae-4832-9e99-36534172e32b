<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>

<head>
<title>Exam 2</title>
<style type="text/css">
body{font-family: Arial, Helvetica, Sans;}
</style>
<meta charset="utf-8" />
</head>

<body>
<h2>Exam 2</h2>

<ol>
<li>
<h4>Question</h4>
Luis quiere ahorrar $75.000 cada mes durante 5 meses. Como ayuda para su negocio, sus padres le han propuesto dos opciones, pero solo puede elegir una de ellas.
<div class="p"><!----></div>
<b>Opci&lt;U+00F3&#62;n 1.</b> Al finalizar cada mes, su abuelo le regala un porcentaje del dinero que tenga acumulado en su negocio.
<div class="p"><!----></div>
<img src="tabla_opcion1.svg" alt="tabla_opcion1.svg" />
<b>Opci&lt;U+00F3&#62;n 2.</b> Al finalizar cada mes, su abuela le regala un porcentaje del dinero que tenga acumulado en su negocio.
<div class="p"><!----></div>
<img src="tabla_opcion2.svg" alt="tabla_opcion2.svg" />
Luis decide elegir la opci&lt;U+00F3&#62;n en que le regalen la mayor cantidad de dinero y elija la abuelo para la elecci&lt;U+00F3&#62;n de Luis?
<div class="p"><!----></div>
<br/>
<ol type="a">
<li>
S&lt;U+00ED&#62;, porque la abuelo da un porcentaje constante del 8
</li>
<li>
S&lt;U+00ED&#62;, porque considerando solo el ahorro base de <math xmlns="http://www.w3.org/1998/Math/MathML">
<mrow><mn>75</mn><mo>.</mo><mn>000</mn><mi mathvariant="italic">por</mi><mi mathvariant="italic">mes</mi><mo>,</mo><mi mathvariant="italic">la</mi><mi mathvariant="italic">diferencia</mi><mi mathvariant="italic">en</mi><mi mathvariant="italic">ayuda</mi><mi mathvariant="italic">no</mi><mi mathvariant="italic">es</mi><mi mathvariant="italic">significativa</mi><mo>.</mo>\<mn>007</mn>\<mn>007</mn>\<mn>007</mn>\<mn>007</mn>\<mn>007</mn><mi>S</mi><mo>&lt;</mo><mi>U</mi><mo>+</mo><mn>00</mn><mi mathvariant="italic">ED</mi><mo>&gt;</mo><mo>,</mo><mi mathvariant="italic">porque</mi><mi mathvariant="italic">la</mi><mi mathvariant="italic">suma</mi><mi mathvariant="italic">de</mi><mi mathvariant="italic">porcentajes</mi><mi mathvariant="italic">del</mi><mi mathvariant="italic">abuela</mi><mo stretchy="false">(</mo><mn>1</mn><mo>+</mo><mn>4</mn><mo>+</mo><mn>15</mn><mo>+</mo><mn>9</mn><mo>+</mo><mn>11</mn><mo>=</mo><mn>40</mn>\<mn>007</mn>\<mn>007</mn>\<mn>007</mn>\<mn>007</mn>\<mn>007</mn><mi mathvariant="italic">No</mi><mo>,</mo><mi mathvariant="italic">porque</mi><mi mathvariant="italic">la</mi><mi mathvariant="italic">ayuda</mi><mi mathvariant="italic">total</mi><mi mathvariant="italic">de</mi><mi mathvariant="italic">su</mi><mi mathvariant="italic">abuela</mi><mi mathvariant="italic">es</mi><mi mathvariant="italic">de</mi></mrow></math>108.750 mientras que la de su abuelo es de 
<ul>
<li> Mes 1: 75.000  8 = 6.000
<li> Mes 2: 150.000  8 = 12.000
<li> Mes 3: 225.000  8 = 18.000</ul>
<ul>
<li> Mes 1: 75.000  1 = 750
<li> Mes 2: 150.000  4 = 6.000
<li> Mes 3: 225.000  15 = 33.750</ul>
<ul>
<li> Error 1: Confundir porcentaje promedio con cU+00E1lculo real sobre montos acumulados
<li> Error 2: Considerar solo el U+00FAltimo mes en lugar del total acumulado
<li> Error 3: Sumar porcentajes directamente sin considerar las bases de cU+00E1lculo
<li> Error 4: Invertir la conclusiU+00F3n por lectura incorrecta de los totales</ul>
</li>
</ol>
<br/>
</li>
</ol>

</body>
</html>
