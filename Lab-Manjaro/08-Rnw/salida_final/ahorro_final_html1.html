<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>

<head>
<title>Exam 1</title>
<style type="text/css">
body{font-family: Arial, Helvetica, Sans;}
</style>
<meta charset="utf-8" />
</head>

<body>
<h2>Exam 1</h2>

<ol>
<li>
<h4>Question</h4>
Sofia quiere ahorrar $125.000 cada mes durante 5 meses. Como ayuda para su ahorro, sus padres le han propuesto dos opciones, pero solo puede elegir una de ellas.
<div class="p"><!----></div>
<b>Opci&lt;U+00F3&#62;n 1.</b> Al finalizar cada mes, su t&lt;U+00ED&#62;o le regala un porcentaje del dinero que tenga acumulado en su ahorro.
<div class="p"><!----></div>
<img src="tabla_opcion1.svg" alt="tabla_opcion1.svg" />
<b>Opci&lt;U+00F3&#62;n 2.</b> Al finalizar cada mes, su t&lt;U+00ED&#62;a le regala un porcentaje del dinero que tenga acumulado en su ahorro.
<div class="p"><!----></div>
<img src="tabla_opcion2.svg" alt="tabla_opcion2.svg" />
Sofia decide elegir la opci&lt;U+00F3&#62;n en que le regalen la mayor cantidad de dinero y elija la t&lt;U+00ED&#62;o para la elecci&lt;U+00F3&#62;n de Sofia?
<div class="p"><!----></div>
<br/>
<ol type="a">
<li>
S&lt;U+00ED&#62;, porque considerando solo el ahorro base de <math xmlns="http://www.w3.org/1998/Math/MathML">
<mrow><mn>125</mn><mo>.</mo><mn>000</mn><mi mathvariant="italic">por</mi><mi mathvariant="italic">mes</mi><mo>,</mo><mi mathvariant="italic">la</mi><mi mathvariant="italic">diferencia</mi><mi mathvariant="italic">en</mi><mi mathvariant="italic">ayuda</mi><mi mathvariant="italic">no</mi><mi mathvariant="italic">es</mi><mi mathvariant="italic">significativa</mi><mo>.</mo>\<mn>007</mn>\<mn>007</mn>\<mn>007</mn>\<mn>007</mn>\<mn>007</mn><mi>S</mi><mo>&lt;</mo><mi>U</mi><mo>+</mo><mn>00</mn><mi mathvariant="italic">ED</mi><mo>&gt;</mo><mo>,</mo><mi mathvariant="italic">porque</mi><mi mathvariant="italic">el</mi><mi mathvariant="italic">porcentaje</mi><mi>m</mi><mo>&lt;</mo><mi>U</mi><mo>+</mo><mn>00</mn><mi>E</mi><mn>1</mn><mo>&gt;</mo><mi mathvariant="italic">ximo</mi><mi mathvariant="italic">del</mi><mi>t</mi><mo>&lt;</mo><mi>U</mi><mo>+</mo><mn>00</mn><mi mathvariant="italic">ED</mi><mo>&gt;</mo><mi>a</mi><mi mathvariant="italic">es</mi><mn>17</mn>\<mn>007</mn>\<mn>007</mn>\<mn>007</mn>\<mn>007</mn>\<mn>007</mn><mi mathvariant="italic">No</mi><mo>,</mo><mi mathvariant="italic">porque</mi><mi mathvariant="italic">la</mi><mi mathvariant="italic">ayuda</mi><mi mathvariant="italic">total</mi><mi mathvariant="italic">de</mi><mi mathvariant="italic">su</mi><mi>t</mi><mo>&lt;</mo><mi>U</mi><mo>+</mo><mn>00</mn><mi mathvariant="italic">ED</mi><mo>&gt;</mo><mi>a</mi><mi mathvariant="italic">es</mi><mi mathvariant="italic">de</mi></mrow></math>188.750 mientras que la de su t&lt;U+00ED&#62;o es de 
<ul>
<li> Mes 1: 125.000 times 15
<li> Mes 2: 250.000 times 15
<li> Mes 3: 375.000 times 15</ul>
<ul>
<li> Mes 1: 125.000 times 1
<li> Mes 2: 250.000 times 3
<li> Mes 3: 375.000 times 17</ul>
<ul>
<li> Error 1: Confundir porcentaje promedio con cU+00E1lculo real sobre montos acumulados
<li> Error 2: Considerar solo el U+00FAltimo mes en lugar del total acumulado
<li> Error 3: Sumar porcentajes directamente sin considerar las bases de cU+00E1lculo
<li> Error 4: Invertir la conclusiU+00F3n por lectura incorrecta de los totales</ul>
</li>
</ol>
<br/>
</li>
</ol>

</body>
</html>
