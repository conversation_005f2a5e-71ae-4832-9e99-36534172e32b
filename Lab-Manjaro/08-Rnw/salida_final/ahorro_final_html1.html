<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>

<head>
<title>Exam 1</title>
<style type="text/css">
body{font-family: Arial, Helvetica, Sans;}
</style>
<meta charset="utf-8" />
</head>

<body>
<h2>Exam 1</h2>

<ol>
<li>
<h4>Question</h4>
Carlos quiere ahorrar $150.000 cada mes durante 5 meses. Como ayuda para su inversi&lt;U+00F3&#62;n, sus padres le han propuesto dos opciones, pero solo puede elegir una de ellas.
<div class="p"><!----></div>
<b>Opci&lt;U+00F3&#62;n 1.</b> Al finalizar cada mes, su t&lt;U+00ED&#62;o le regala un porcentaje del dinero que tenga acumulado en su inversi&lt;U+00F3&#62;n.
<div class="p"><!----></div>
<img src="tabla_opcion1.svg" alt="tabla_opcion1.svg" />
<b>Opci&lt;U+00F3&#62;n 2.</b> Al finalizar cada mes, su t&lt;U+00ED&#62;a le regala un porcentaje del dinero que tenga acumulado en su inversi&lt;U+00F3&#62;n.
<div class="p"><!----></div>
<img src="tabla_opcion2.svg" alt="tabla_opcion2.svg" />
Carlos decide elegir la opci&lt;U+00F3&#62;n en que le regalen la mayor cantidad de dinero y elija la t&lt;U+00ED&#62;o para la elecci&lt;U+00F3&#62;n de Carlos?
<div class="p"><!----></div>
<br/>
<ol type="a">
<li>
No, porque la ayuda total de su t&lt;U+00ED&#62;a es de <math xmlns="http://www.w3.org/1998/Math/MathML">
<mrow><mn>220</mn><mo>.</mo><mn>500</mn><mi mathvariant="italic">mientras</mi><mi mathvariant="italic">que</mi><mi mathvariant="italic">la</mi><mi mathvariant="italic">de</mi><mi mathvariant="italic">su</mi><mi>t</mi><mo>&lt;</mo><mi>U</mi><mo>+</mo><mn>00</mn><mi mathvariant="italic">ED</mi><mo>&gt;</mo><mi>o</mi><mi mathvariant="italic">es</mi><mi mathvariant="italic">de</mi></mrow></math>180.000.
</li>
<li>
S&lt;U+00ED&#62;, porque el porcentaje m&lt;U+00E1&#62;ximo del t&lt;U+00ED&#62;a es 16
</li>
<li>
S&lt;U+00ED&#62;, porque en el &lt;U+00FA&#62;ltimo mes la ayuda del t&lt;U+00ED&#62;a es de <math xmlns="http://www.w3.org/1998/Math/MathML">
<mrow><mn>67</mn><mo>.</mo><mn>500</mn><mi>y</mi><mi mathvariant="italic">la</mi><mi mathvariant="italic">de</mi><mi mathvariant="italic">la</mi><mi>t</mi><mo>&lt;</mo><mi>U</mi><mo>+</mo><mn>00</mn><mi mathvariant="italic">ED</mi><mo>&gt;</mo><mi>o</mi><mi mathvariant="italic">es</mi><mi mathvariant="italic">de</mi></mrow></math>60.000.
</li>
<li>
S&lt;U+00ED&#62;, porque la suma de porcentajes del t&lt;U+00ED&#62;a (2+6+16+10+9=43
</li>
</ol>
<br/>
<h4>Solution</h4>
<div class="p"><!----></div>
Para resolver este problema, debemos calcular la ayuda total que recibir&lt;U+00ED&#62;a Carlos con cada opci&lt;U+00F3&#62;n.
<div class="p"><!----></div>
<b>Opci&lt;U+00F3&#62;n 1 (t&lt;U+00ED&#62;o):</b>
<ul>
<li> Mes 1: $150.000 <math xmlns="http://www.w3.org/1998/Math/MathML">
<mrow><mo>&times;</mo></mrow></math> 8% = $12.000
<div class="p"><!----></div>
</li>
<li> Mes 2: $3e+05 <math xmlns="http://www.w3.org/1998/Math/MathML">
<mrow><mo>&times;</mo></mrow></math> 8% = $24.000
<div class="p"><!----></div>
</li>
<li> Mes 3: $450.000 <math xmlns="http://www.w3.org/1998/Math/MathML">
<mrow><mo>&times;</mo></mrow></math> 8% = $36.000
<div class="p"><!----></div>
</li>
</ul>
Total t&lt;U+00ED&#62;o: $180.000
<div class="p"><!----></div>
<b>Opci&lt;U+00F3&#62;n 2 (t&lt;U+00ED&#62;a):</b>
<ul>
<li> Mes 1: $150.000 <math xmlns="http://www.w3.org/1998/Math/MathML">
<mrow><mo>&times;</mo></mrow></math> 2% = $3.000
<div class="p"><!----></div>
</li>
<li> Mes 2: $3e+05 <math xmlns="http://www.w3.org/1998/Math/MathML">
<mrow><mo>&times;</mo></mrow></math> 6% = $18.000
<div class="p"><!----></div>
</li>
<li> Mes 3: $450.000 <math xmlns="http://www.w3.org/1998/Math/MathML">
<mrow><mo>&times;</mo></mrow></math> 16% = $72.000
<div class="p"><!----></div>
</li>
</ul>
Total t&lt;U+00ED&#62;a: $220.500
<div class="p"><!----></div>
Por lo tanto, la respuesta correcta es la opci&lt;U+00F3&#62;n que indica que el t&lt;U+00ED&#62;a da m&lt;U+00E1&#62;s ayuda.
<div class="p"><!----></div>
<b>An&lt;U+00E1&#62;lisis de errores comunes:</b>
<ul>
<li> <b>Error 1</b>: Confundir porcentaje promedio con c&lt;U+00E1&#62;lculo real sobre montos acumulados
<div class="p"><!----></div>
</li>
<li> <b>Error 2</b>: Considerar solo el &lt;U+00FA&#62;ltimo mes en lugar del total acumulado
<div class="p"><!----></div>
</li>
<li> <b>Error 3</b>: Sumar porcentajes directamente sin considerar las bases de c&lt;U+00E1&#62;lculo
<div class="p"><!----></div>
</li>
<li> <b>Error 4</b>: Invertir la conclusi&lt;U+00F3&#62;n por lectura incorrecta de los totales
<div class="p"><!----></div>
</li>
</ul>
<div class="p"><!----></div>
<br/>
<ol type="a">
<li>
Verdadero. Esta opci&lt;U+00F3&#62;n calcula correctamente los totales de ayuda para ambas opciones y compara adecuadamente cu&lt;U+00E1&#62;l es mayor.
</li>
<li>
Falso. Esta opci&lt;U+00F3&#62;n contiene un error en el razonamiento o c&lt;U+00E1&#62;lculo matem&lt;U+00E1&#62;tico.
</li>
<li>
Falso. Esta opci&lt;U+00F3&#62;n contiene un error en el razonamiento o c&lt;U+00E1&#62;lculo matem&lt;U+00E1&#62;tico.
</li>
<li>
Falso. Esta opci&lt;U+00F3&#62;n suma porcentajes directamente sin considerar que se aplican sobre diferentes bases de c&lt;U+00E1&#62;lculo.
</li>
</ol>
<br/>
</li>
</ol>

</body>
</html>
