\documentclass[10pt,a4paper]{article}

%% paquetes básicos
\usepackage[utf8]{inputenc}
\usepackage[spanish]{babel}
\usepackage{a4wide,color,verbatim,Sweave,url,xargs,amsmath,booktabs,longtable}
\usepackage{graphicx,float}
\usepackage{tikz,xcolor}
\usepackage{enumitem}
\usepackage{colortbl}

%% bibliotecas TikZ según necesidad
\usetikzlibrary{automata,positioning,calc,arrows}

%% entornos para exams
\newenvironment{question}{\item}{}
\newenvironment{solution}{\comment}{\endcomment}
\newenvironment{answerlist}{\renewcommand{\labelenumii}{(\alph{enumii})}\begin{enumerate}}{\end{enumerate}}

%% comandos para metadatos exams
\newcommand{\exname}[1]{\def\@exname{#1}}
\newcommand{\extype}[1]{\def\@extype{#1}}
\newcommand{\exsolution}[1]{\def\@exsolution{#1}}
\newcommand{\exshuffle}[1]{\def\@exshuffle{#1}}
\newcommand{\exsection}[1]{\def\@exsection{#1}}

%% configuración párrafos
\setlength{\parskip}{0.7ex plus0.1ex minus0.1ex}
\setlength{\parindent}{0em}

\begin{document}
\input{Ahorro_opciones_porcentaje_interpretacion_representacion_n2_v1-concordance}

\begin{enumerate}


\begin{question}

Carlos quiere ahorrar \$1e+05 cada mes durante 3 meses. Como ayuda para su negocio, sus padres le han propuesto dos opciones, pero solo puede elegir una de ellas.

\textbf{Opci<U+00F3>n 1.} Al finalizar cada mes, su mam<U+00E1> le regala un porcentaje del dinero que tenga acumulado en su negocio.

\includegraphics[width=8cm]{tabla_opcion1.png}
\textbf{Opci<U+00F3>n 2.} Al finalizar cada mes, su pap<U+00E1> le regala un porcentaje del dinero que tenga acumulado en su negocio.

\includegraphics[width=8cm]{tabla_opcion2.png}
Carlos decide elegir la opci<U+00F3>n en que le regalen la mayor cantidad de dinero y elija la mam<U+00E1> para la elecci<U+00F3>n de Carlos?

\begin{answerlist}
  \item S<U+00ED>, porque la ayuda total de su mam<U+00E1> es de $60.000 mientras que la de su pap<U+00E1> es de $56.000.
  \item No, porque considerando solo el ahorro base de $1e+05 por mes, la diferencia en ayuda no es significativa.
  \item No, porque la ayuda total de su pap<U+00E1> es de $56.000 mientras que la de su mam<U+00E1> es de $60.000.
  \item No, porque la mam<U+00E1> da un porcentaje constante del 10% que es m<U+00E1>s confiable que los porcentajes variables del pap<U+00E1>.
\end{answerlist}
\end{question}

\begin{solution}

Para resolver este problema, debemos calcular la ayuda total que recibir<U+00ED>a Carlos con cada opci<U+00F3>n.

\textbf{Opci<U+00F3>n 1 (mam<U+00E1>):}
\begin{itemize}
\item Mes 1: \$1e+05 $\times$ 10\% = \$10.000
\item Mes 2: \$2e+05 $\times$ 10\% = \$20.000
\item Mes 3: \$3e+05 $\times$ 10\% = \$30.000
\end{itemize}
Total mam<U+00E1>: \$60.000

\textbf{Opci<U+00F3>n 2 (pap<U+00E1>):}
\begin{itemize}
\item Mes 1: \$1e+05 $\times$ 2\% = \$2.000
\item Mes 2: \$2e+05 $\times$ 6\% = \$12.000
\item Mes 3: \$3e+05 $\times$ 14\% = \$42.000
\end{itemize}
Total pap<U+00E1>: \$56.000

Por lo tanto, la respuesta correcta es la opci<U+00F3>n que indica que la mam<U+00E1> da m<U+00E1>s ayuda.

\textbf{An<U+00E1>lisis de errores comunes:}
\begin{itemize}
\item \textbf{Error 1}: Confundir porcentaje promedio con c<U+00E1>lculo real sobre montos acumulados
\item \textbf{Error 2}: Considerar solo el <U+00FA>ltimo mes en lugar del total acumulado
\item \textbf{Error 3}: Sumar porcentajes directamente sin considerar las bases de c<U+00E1>lculo
\item \textbf{Error 4}: Invertir la conclusi<U+00F3>n por lectura incorrecta de los totales
\end{itemize}

\begin{answerlist}
  \item Verdadero. Esta opci<U+00F3>n calcula correctamente los totales de ayuda para ambas opciones y compara adecuadamente cu<U+00E1>l es mayor.
  \item Falso. Esta opci<U+00F3>n contiene un error en el razonamiento o c<U+00E1>lculo matem<U+00E1>tico.
  \item Falso. Esta opci<U+00F3>n invierte la conclusi<U+00F3>n correcta, confundiendo cu<U+00E1>l familiar da mayor ayuda total.
  \item Falso. Esta opci<U+00F3>n confunde la magnitud de los porcentajes con el resultado final del c<U+00E1>lculo.
\end{answerlist}
\end{solution}

%% META-INFORMATION
\exname{Ahorro con porcentajes - Comparaci<U+00F3>n de opciones}
\extype{schoice}
\exsolution{1000}
\exshuffle{TRUE}
\exsection{Pensamiento Aleatorio}

\end{enumerate}
\end{document}
